class AuthService {
    constructor() {
        this.baseUrl = '/api'; // 使用代理路径
        this.tokenKey = 'unified_token';
        this.refreshTokenKey = 'refresh_token';
        this.userInfoKey = 'user_info';
        this.permissionsKey = 'permissions';
        this.rolesKey = 'roles';
    }

    /**
     * 用户登录
     * @param {string} userName - 用户名
     * @param {string} password - 密码
     * @returns {Promise} 登录结果
     */
    async login(userName, password) {
        try {
            const response = await fetch(`${this.baseUrl}/auth/user_name`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ userName, password })
            });

            const result = await response.json();
            
            // 检查返回状态码
            if (result.code === 200) {
                // 保存令牌
                localStorage.setItem(this.tokenKey, result.data.token);
                
                return {
                    success: true,
                    data: result.data
                };
            } else {
                throw new Error(result.message || '登录失败');
            }
        } catch (error) {
            console.error('登录失败:', error);
            throw error;
        }
    }

    /**
     * 用户注册
     * @param {Object} registerData - 注册数据
     * @param {string} registerData.userName - 用户名
     * @param {string} registerData.password - 密码
     * @param {string} [registerData.email] - 邮箱
     * @param {string} [registerData.phone] - 手机号
     * @param {string} [registerData.realName] - 真实姓名
     * @returns {Promise} 注册结果
     */
    async register(registerData) {
        try {
            const response = await fetch(`${this.baseUrl}/auth/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(registerData)
            });

            const result = await response.json();
            
            // 检查返回状态码
            if (result.code === 200) {
                return {
                    success: true,
                    data: result.data,
                    message: result.message || '注册成功'
                };
            } else {
                throw new Error(result.message || '注册失败');
            }
        } catch (error) {
            console.error('注册失败:', error);
            throw error;
        }
    }

    /**
     * 获取用户信息
     * @returns {Promise} 用户信息
     */
    async fetchUserInfo() {
        try {
            const response = await fetch(`${this.baseUrl}/auth/user_info`, {
                headers: {
                    'Authorization': `Bearer ${this.getToken()}`
                }
            });

            const result = await response.json();
            
            // 检查返回状态码
            if (result.code === 200) {
                const userInfo = result.data;
                // 保存用户基本信息
                localStorage.setItem(this.userInfoKey, JSON.stringify({
                    id: userInfo.id,
                    userName: userInfo.userName,
                    nickName: userInfo.nickName,
                    realName: userInfo.realName,
                    avatar: userInfo.avatar,
                    email: userInfo.email,
                    phone: userInfo.phone,
                    status: userInfo.status
                }));

                // 保存角色信息
                if (userInfo.roleIds) {
                    localStorage.setItem(this.rolesKey, JSON.stringify(userInfo.roleIds));
                }

                // 保存权限信息
                if (userInfo.permissions) {
                    localStorage.setItem(this.permissionsKey, JSON.stringify(userInfo.permissions));
                }

                // 触发用户信息更新事件
                this.dispatchUserInfoEvent(userInfo);

                return {
                    success: true,
                    data: userInfo
                };
            } else {
                throw new Error(result.message || '获取用户信息失败');
            }
        } catch (error) {
            console.error('获取用户信息失败:', error);
            // 如果是 401 错误，尝试刷新令牌
            if (error.response?.status === 401) {
                try {
                    await this.refreshToken();
                    // 重新获取用户信息
                    return await this.fetchUserInfo();
                } catch (refreshError) {
                    this.clearAuth();
                    throw new Error('登录已过期，请重新登录');
                }
            }
            throw error;
        }
    }

    /**
     * 触发用户信息更新事件
     * @param {Object} userInfo - 用户信息
     */
    dispatchUserInfoEvent(userInfo) {
        const event = new CustomEvent('userInfoUpdate', {
            detail: userInfo
        });
        window.dispatchEvent(event);
    }

    /**
     * 刷新令牌
     * @returns {Promise} 新的令牌
     */
    async refreshToken() {
        const refreshToken = localStorage.getItem(this.refreshTokenKey);
        if (!refreshToken) {
            throw new Error('无刷新令牌');
        }

        try {
            const response = await fetch(`${this.baseUrl}/auth/refresh_token`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ refreshToken })
            });

            const result = await response.json();
            
            if (result.code === 200) {
                localStorage.setItem(this.tokenKey, result.data.token);
                if (result.data.refreshToken) {
                    localStorage.setItem(this.refreshTokenKey, result.data.refreshToken);
                }
                return {
                    success: true,
                    data: result.data
                };
            } else {
                throw new Error(result.message || '刷新令牌失败');
            }
        } catch (error) {
            console.error('刷新令牌失败:', error);
            this.clearAuth();
            throw error;
        }
    }

    /**
     * 退出登录
     * @returns {Promise} 退出结果
     */
    async logout() {
        try {
            const response = await fetch(`${this.baseUrl}/auth/logout`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.getToken()}`
                }
            });

            const result = await response.json();
            
            // 无论成功失败都清除本地存储
            this.clearAuth();
            
            if (result.code !== 200) {
                throw new Error(result.message || '退出登录失败');
            }

            // 触发退出登录事件
            this.dispatchLogoutEvent();
        } catch (error) {
            console.error('退出登录失败:', error);
            // 无论如何都清除本地存储
            this.clearAuth();
            throw error;
        }
    }

    /**
     * 触发退出登录事件
     */
    dispatchLogoutEvent() {
        const event = new CustomEvent('logout');
        window.dispatchEvent(event);
    }

    /**
     * 获取当前令牌
     * @returns {string} 令牌
     */
    getToken() {
        return localStorage.getItem(this.tokenKey);
    }

    /**
     * 获取当前用户信息
     * @returns {Object} 用户信息
     */
    getUserInfo() {
        const userInfo = localStorage.getItem(this.userInfoKey);
        return userInfo ? JSON.parse(userInfo) : null;
    }

    /**
     * 获取用户角色
     * @returns {Array} 角色列表
     */
    getRoles() {
        const roles = localStorage.getItem(this.rolesKey);
        return roles ? JSON.parse(roles) : [];
    }

    /**
     * 获取用户权限
     * @returns {Array} 权限列表
     */
    getPermissions() {
        const permissions = localStorage.getItem(this.permissionsKey);
        return permissions ? JSON.parse(permissions) : [];
    }

    /**
     * 检查是否有指定权限
     * @param {string} permission - 权限标识
     * @returns {boolean} 是否有权限
     */
    hasPermission(permission) {
        const permissions = this.getPermissions();
        return permissions.includes(permission);
    }

    /**
     * 检查是否有指定角色
     * @param {string} role - 角色标识
     * @returns {boolean} 是否有角色
     */
    hasRole(role) {
        const roles = this.getRoles();
        return roles.includes(role);
    }

    /**
     * 检查是否已登录
     * @returns {boolean} 是否已登录
     */
    isLoggedIn() {
        return !!this.getToken();
    }

    /**
     * 清除认证信息
     */
    clearAuth() {
        localStorage.removeItem(this.tokenKey);
        localStorage.removeItem(this.refreshTokenKey);
        localStorage.removeItem(this.userInfoKey);
        localStorage.removeItem(this.permissionsKey);
        localStorage.removeItem(this.rolesKey);
    }
}

// 创建全局认证服务实例
window.authService = new AuthService(); 