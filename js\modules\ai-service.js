/**
 * AI服务模块 - 集成Dify和DeepSeek
 * 支持多种AI服务调用方式
 */

class AIService {
    constructor(config = {}) {
        this.config = {
            // Dify配置
            dify: {
                apiBase: config.DIFY_BASE_URL || 'https://api.dify.ai/v1',
                apiKey: config.DIFY_API_KEY || 'sk-4e0c0a67bf0a4ff59317aa814a016c8c',
                appType: config.DIFY_APP_TYPE || 'chatbot' // chatbot, agent, workflow
            },
            // DeepSeek配置
            deepseek: {
                apiBase: config.DEEPSEEK_BASE_URL || config.BASE_URL || 'https://api.deepseek.com/v1',
                apiKey: config.DEEPSEEK_API_KEY || config.API_KEY || '',
                model: config.MODEL || 'deepseek-chat' // deepseek-chat, deepseek-reasoner
            },
            // 当前使用的服务
            activeService: config.SERVICE_TYPE || 'deepseek' // dify, deepseek, hybrid
        };
        
        console.log('AIService配置已初始化:', {
            activeService: this.config.activeService,
            hasDeepSeekKey: !!this.config.deepseek.apiKey,
            hasDifyKey: !!this.config.dify.apiKey,
            deepseekConfig: {
                apiBase: this.config.deepseek.apiBase,
                apiKeyLength: this.config.deepseek.apiKey.length,
                model: this.config.deepseek.model
            }
        });
    }

    // 初始化配置
    init(config) {
        this.config = { ...this.config, ...config };
        console.log('AI服务初始化完成，当前服务：', this.config.activeService);
    }

    // 生成姻缘画像
    async generatePortrait(userPreferences) {
        try {
            switch (this.config.activeService) {
                case 'dify':
                    return await this.callDifyAPI(userPreferences);
                case 'deepseek':
                    return await this.callDeepSeekAPI(userPreferences);
                case 'hybrid':
                    return await this.callHybridAPI(userPreferences);
                default:
                    throw new Error('未知的服务类型');
            }
        } catch (error) {
            console.error('服务调用失败:', error);
            throw error;
        }
    }

    // 调用Dify API
    async callDifyAPI(userPreferences) {
        const prompt = this.buildDifyPrompt(userPreferences);
        
        const requestBody = {
            inputs: {
                user_preferences: JSON.stringify(userPreferences),
                query: prompt
            },
            query: prompt,
            response_mode: "blocking",
            user: `user_${Date.now()}`,
            conversation_id: ""
        };

        // 根据应用类型调整请求体
        let endpoint = '/chat-messages';
        if (this.config.dify.appType === 'workflow') {
            endpoint = '/workflows/run';
            requestBody.inputs.query = prompt;
        } else if (this.config.dify.appType === 'completion') {
            endpoint = '/completion-messages';
        }

        const response = await fetch(`${this.config.dify.apiBase}${endpoint}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.config.dify.apiKey}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`Dify API调用失败: ${response.status}`);
        }

        const data = await response.json();
        return this.parseDifyResponse(data, userPreferences);
    }

    // 调用DeepSeek API
    async callDeepSeekAPI(userPreferences) {
        const prompt = this.buildDeepSeekPrompt(userPreferences);

        const requestBody = {
            model: this.config.deepseek.model,
            messages: [
                {
                    role: "system",
                    content: "你是一个专业的姻缘分析师，能够根据用户的偏好生成详细的理想另一半画像。请用中文回复，并按照指定的JSON格式返回结果。"
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            stream: false,
            temperature: 0.7,
            max_tokens: 2000
        };

        try {
            const response = await fetch(`${this.config.deepseek.apiBase}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.config.deepseek.apiKey}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('DeepSeek API错误响应:', response.status, errorText);
                throw new Error(`DeepSeek API调用失败: ${response.status} - ${errorText}`);
            }

            const data = await response.json();
            return this.parseDeepSeekResponse(data, userPreferences);
        } catch (error) {
            console.error('DeepSeek API调用失败:', error);
            throw error;
        }
    }

    // 混合调用 (Dify工作流 + DeepSeek推理)
    async callHybridAPI(userPreferences) {
        try {
            // 首先用Dify工作流进行初步分析
            const difyResult = await this.callDifyAPI(userPreferences);
            
            // 然后用DeepSeek进行深度推理优化
            const enhancedPrompt = this.buildEnhancementPrompt(difyResult, userPreferences);
            
            const requestBody = {
                model: 'deepseek-reasoner', // 使用推理模型
                messages: [
                    {
                        role: "system",
                        content: "你是一个高级姻缘分析专家，请基于初步分析结果，进行更深入的推理分析，优化匹配建议。"
                    },
                    {
                        role: "user",
                        content: enhancedPrompt
                    }
                ],
                stream: false,
                temperature: 0.6
            };

            const response = await fetch(`${this.config.deepseek.apiBase}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.config.deepseek.apiKey}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            const data = await response.json();
            return this.mergeResults(difyResult, data, userPreferences);
        } catch (error) {
            console.warn('混合调用失败，降级到单一服务:', error);
            return await this.callDifyAPI(userPreferences);
        }
    }

    // 构建Dify提示词
    buildDifyPrompt(userPreferences) {
        const {
            userZodiac,
            preferredGender,
            preferredAge,
            priorities
        } = userPreferences;

        return `请为我生成一个理想另一半的画像：
用户偏好：
- 理想对象性别：${preferredGender === 'male' ? '男性' : '女性'}
- 理想年龄段：${this.getAgeRangeText(preferredAge)}
- 用户星座：${userZodiac || '未提供'}
- 最看重的特质：${priorities.join('、')}

请生成包含姓名、年龄、外貌、性格、兴趣爱好、职业等详细信息的画像。`;
    }

    // 构建DeepSeek提示词
    buildDeepSeekPrompt(userPreferences) {
        const {
            userZodiac,
            preferredGender,
            preferredAge,
            priorities
        } = userPreferences;

        return `作为专业的姻缘分析师，请根据以下用户偏好生成一个详细的理想另一半画像：

用户偏好分析：
- 理想对象性别：${preferredGender === 'male' ? '男性' : '女性'}
- 理想年龄段：${this.getAgeRangeText(preferredAge)}
- 用户星座：${userZodiac || '未提供'}
- 最看重的特质：${priorities.map(p => this.getPriorityText(p)).join('、')}

请生成一个JSON格式的画像，包含以下字段：
{
  "basic": {
    "name": "姓名",
    "age": 具体年龄,
    "zodiac": "星座",
    "avatar": "emoji头像"
  },
  "appearance": ["外貌特征1", "外貌特征2"],
  "personality": ["性格特点1", "性格特点2"],
  "lifestyle": ["生活方式1", "生活方式2"],
  "career": "职业描述",
  "description": "综合描述",
  "match": {
    "overall": 匹配度分数(75-99),
    "reasons": ["匹配原因1", "匹配原因2"]
  },
  "suggestions": {
    "dating": ["约会建议1", "约会建议2"],
    "communication": ["沟通建议1", "沟通建议2"],
    "challenges": ["注意事项1", "注意事项2"]
  }
}

请确保生成的内容符合中国文化背景，并且积极正面。`;
    }

    // 构建增强提示词
    buildEnhancementPrompt(difyResult, userPreferences) {
        return `请基于以下初步分析结果，进行更深入的姻缘匹配分析：

初步分析结果：
${JSON.stringify(difyResult, null, 2)}

用户偏好：
${JSON.stringify(userPreferences, null, 2)}

请进行以下优化：
1. 更精准的匹配度计算
2. 更个性化的建议
3. 更深入的心理分析
4. 更实用的相处指导

请返回优化后的完整画像结果。`;
    }

    // 解析Dify响应
    parseDifyResponse(data, userPreferences) {
        const content = data.answer || data.data?.outputs?.text || data.text || '';
        
        try {
            // 尝试解析JSON格式的响应
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return this.normalizeResult(parsed, userPreferences);
            }
        } catch (e) {
            console.warn('Dify响应不是JSON格式，使用文本解析');
        }

        // 文本格式响应的简单解析
        return this.parseTextResponse(content, userPreferences);
    }

    // 解析DeepSeek响应
    parseDeepSeekResponse(data, userPreferences) {
        const content = data.choices[0].message.content;
        
        try {
            // 尝试解析JSON格式
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return this.normalizeResult(parsed, userPreferences);
            }
        } catch (e) {
            console.warn('DeepSeek响应解析失败，使用默认格式');
        }

        return this.parseTextResponse(content, userPreferences);
    }

    // 合并混合调用结果
    mergeResults(difyResult, deepseekData, userPreferences) {
        const deepseekContent = deepseekData.choices[0].message.content;
        
        try {
            const enhanced = JSON.parse(deepseekContent);
            return {
                ...difyResult,
                ...enhanced,
                match: {
                    ...difyResult.match,
                    ...enhanced.match,
                    enhanced: true
                },
                suggestions: {
                    ...difyResult.suggestions,
                    ...enhanced.suggestions
                }
            };
        } catch (e) {
            return difyResult; // 降级返回Dify结果
        }
    }

    // 标准化结果格式
    normalizeResult(result, userPreferences) {
        const genderProfiles = {
            male: {
                names: ['志强', '俊杰', '文轩', '浩然', '子涵', '梓豪'],
                avatars: ['👨', '🧑', '👨‍💼', '👨‍🎓', '👨‍🎨', '👨‍💻']
            },
            female: {
                names: ['雨萱', '诗涵', '梦琪', '语嫣', '思怡', '婉儿'],
                avatars: ['👩', '👩‍💼', '👩‍🎓', '👩‍🎨', '👩‍🌾', '👩‍⚕️']
            }
        };

        const profiles = genderProfiles[userPreferences.preferredGender];
        
        return {
            basic: {
                name: result.basic?.name || profiles.names[Math.floor(Math.random() * profiles.names.length)],
                age: result.basic?.age || this.generateAge(userPreferences.preferredAge),
                zodiac: result.basic?.zodiac || this.getRandomZodiac(),
                avatar: result.basic?.avatar || profiles.avatars[Math.floor(Math.random() * profiles.avatars.length)]
            },
            match: {
                overall: result.match?.overall || Math.floor(Math.random() * 20) + 80,
                zodiacMatch: result.match?.zodiacMatch || Math.floor(Math.random() * 15) + 85,
                personalityMatch: result.match?.personalityMatch || Math.floor(Math.random() * 15) + 85,
                lifestyleMatch: result.match?.lifestyleMatch || Math.floor(Math.random() * 15) + 85,
                zodiacCompatibility: result.match?.zodiacCompatibility || '高度匹配'
            },
            description: result.description || '这是一个充满魅力的人，等待着与您相遇。',
            suggestions: {
                dating: result.suggestions?.dating || ['可以尝试去咖啡馆安静地聊天', '一起看场电影，分享彼此的感受'],
                communication: result.suggestions?.communication || ['保持开放和诚实的沟通', '倾听对方的想法和感受'],
                challenges: result.suggestions?.challenges || ['需要时间相互了解', '保持耐心和理解']
            },
            timestamp: new Date().toISOString(),
            aiService: this.config.activeService
        };
    }

    // 文本响应解析
    parseTextResponse(content, userPreferences) {
        // 简单的文本解析逻辑
        const profiles = {
            male: {
                names: ['志强', '俊杰', '文轩', '浩然'],
                avatars: ['👨', '🧑', '👨‍💼', '👨‍🎓']
            },
            female: {
                names: ['雨萱', '诗涵', '梦琪', '语嫣'],
                avatars: ['👩', '👩‍💼', '👩‍🎓', '👩‍🎨']
            }
        };

        const profile = profiles[userPreferences.preferredGender];
        
        return {
            basic: {
                name: profile.names[Math.floor(Math.random() * profile.names.length)],
                age: this.generateAge(userPreferences.preferredAge),
                zodiac: this.getRandomZodiac(),
                avatar: profile.avatars[Math.floor(Math.random() * profile.avatars.length)]
            },
            match: {
                overall: Math.floor(Math.random() * 20) + 80,
                zodiacMatch: Math.floor(Math.random() * 15) + 85,
                personalityMatch: Math.floor(Math.random() * 15) + 85,
                lifestyleMatch: Math.floor(Math.random() * 15) + 85,
                zodiacCompatibility: '高度匹配'
            },
            description: content.substring(0, 200) + '...',
            suggestions: {
                dating: ['尝试一起参加有趣的活动', '在轻松的环境中增进了解'],
                communication: ['保持真诚的交流', '分享彼此的想法'],
                challenges: ['需要磨合期', '保持耐心']
            },
            timestamp: new Date().toISOString(),
            aiService: this.config.activeService
        };
    }

    // 辅助方法
    getAgeRangeText(ageRange) {
        const ranges = {
            'young': '20-25岁',
            'mature': '26-35岁',
            'middle': '36-45岁',
            'senior': '45岁以上'
        };
        return ranges[ageRange] || '26-35岁';
    }

    getPriorityText(priority) {
        const texts = {
            'appearance': '外貌出众',
            'personality': '性格温和',
            'intelligence': '聪明睿智',
            'humor': '幽默风趣',
            'career': '事业有成',
            'family': '顾家温馨',
            'adventure': '喜欢冒险',
            'stability': '稳重可靠'
        };
        return texts[priority] || priority;
    }

    generateAge(ageRange) {
        const ranges = {
            'young': [20, 25],
            'mature': [26, 35],
            'middle': [36, 45],
            'senior': [45, 60]
        };
        const [min, max] = ranges[ageRange] || [26, 35];
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    getRandomZodiac() {
        const zodiacs = ['白羊座', '金牛座', '双子座', '巨蟹座', '狮子座', '处女座', 
                        '天秤座', '天蝎座', '射手座', '摩羯座', '水瓶座', '双鱼座'];
        return zodiacs[Math.floor(Math.random() * zodiacs.length)];
    }

    // 直接调用DeepSeek API（用于画像等功能）
    async callDeepSeek(prompt, systemPrompt = null) {
        console.log('调用DeepSeek API，提示词:', prompt);
        console.log('DeepSeek配置检查:', {
            hasApiKey: !!this.config.deepseek.apiKey,
            apiKeyLength: this.config.deepseek.apiKey ? this.config.deepseek.apiKey.length : 0,
            apiBase: this.config.deepseek.apiBase,
            model: this.config.deepseek.model
        });
        
        if (!this.config.deepseek.apiKey) {
            console.error('DeepSeek API密钥未设置');
            throw new Error('DeepSeek API密钥未设置，请在配置中提供有效的API密钥');
        }
        
        // 如果没有提供自定义system prompt，使用默认的姻缘分析师prompt
        const defaultSystemPrompt = "你是一个专业的姻缘分析师，能够根据用户的偏好生成详细的理想另一半画像。请用中文回复，并按照指定的JSON格式返回结果。";
        
        const requestBody = {
            model: this.config.deepseek.model,
            messages: [
                {
                    role: "system",
                    content: systemPrompt || defaultSystemPrompt
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            stream: false,
            temperature: 0.7,
            max_tokens: 2000
        };
        
        console.log('DeepSeek请求配置:', {
            url: `${this.config.deepseek.apiBase}/chat/completions`,
            model: this.config.deepseek.model,
            hasAuthHeader: !!this.config.deepseek.apiKey
        });
        
        try {
            const response = await fetch(`${this.config.deepseek.apiBase}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.config.deepseek.apiKey}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });
            if (!response.ok) {
                const errorText = await response.text();
                console.error('DeepSeek API错误响应:', response.status, errorText);
                throw new Error(`DeepSeek API调用失败: ${response.status} - ${errorText}`);
            }
            const data = await response.json();
            if (!data || !data.choices || !data.choices[0] || !data.choices[0].message) {
                throw new Error('DeepSeek API返回格式不正确');
            }
            const result = data.choices[0].message.content;
            console.log('DeepSeek API响应成功:', result.substring(0, 100) + '...');
            return result;
        } catch (error) {
            console.error('DeepSeek API调用失败:', error);
            throw error;
        }
    }

    // 直接调用Dify API（用于起名等功能）
    async callDify(prompt) {
        console.log('调用Dify API，提示词:', prompt);
        
        const requestBody = {
            inputs: {
                query: prompt
            },
            query: prompt,
            response_mode: "blocking",
            user: `user_${Date.now()}`,
            conversation_id: ""
        };

        try {
            const response = await fetch(`${this.config.dify.apiBase}/chat-messages`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.config.dify.apiKey}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Dify API错误响应:', response.status, errorText);
                throw new Error(`Dify API调用失败: ${response.status} - ${errorText}`);
            }

            const data = await response.json();
            const result = data.answer || data.data?.outputs?.text || data.text || '';
            console.log('Dify API响应成功:', result);
            return result;
            
        } catch (error) {
            console.error('Dify API调用失败:', error);
            throw error;
        }
    }
}

// 导出服务
window.AIService = AIService; 