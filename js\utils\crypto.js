/**
 * 加密工具类
 */
class CryptoUtil {
    /**
     * SHA256 加密
     * @param {string} text - 要加密的文本
     * @returns {string} 加密后的文本
     */
    static sha256(text) {
        const crypto = window.crypto || window.msCrypto;
        const encoder = new TextEncoder();
        const data = encoder.encode(text);
        
        return crypto.subtle.digest('SHA-256', data)
            .then(buffer => {
                const hashArray = Array.from(new Uint8Array(buffer));
                return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
            });
    }
}

// 导出工具类
window.cryptoUtil = CryptoUtil; 