/**
 * 会员服务模块
 * 提供会员登录、注册、信息管理等功能
 */

class MemberService {
    constructor() {
        this.baseUrl = '/api/member';
        this.currentMember = null;
        this.tokenKey = 'member_token';
        this.memberInfoKey = 'member_info';
    }

    /**
     * 会员登录
     * @param {string} userName - 用户名
     * @param {string} password - 密码
     * @returns {Promise<Object>} 登录结果
     */
    async login(userName, password) {
        try {
            const response = await fetch(`${this.baseUrl}/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    userName: userName,
                    password: password
                })
            });

            const result = await response.json();
            
            if (result.code === 200) {
                // 保存会员信息到本地存储
                this.currentMember = result.data;
                localStorage.setItem(this.memberInfoKey, JSON.stringify(result.data));
                
                // 生成简单的token（实际项目中应该使用JWT）
                const token = btoa(JSON.stringify({
                    id: result.data.id,
                    userName: result.data.userName,
                    timestamp: Date.now()
                }));
                localStorage.setItem(this.tokenKey, token);
                
                return {
                    success: true,
                    data: result.data,
                    message: '登录成功'
                };
            } else {
                return {
                    success: false,
                    message: result.message || '登录失败'
                };
            }
        } catch (error) {
            console.error('登录失败:', error);
            return {
                success: false,
                message: '网络错误，请稍后重试'
            };
        }
    }

    /**
     * 会员注册
     * @param {Object} registerData - 注册数据
     * @returns {Promise<Object>} 注册结果
     */
    async register(registerData) {
        try {
            const response = await fetch(`${this.baseUrl}/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(registerData)
            });

            const result = await response.json();
            
            if (result.code === 200) {
                return {
                    success: true,
                    message: '注册成功'
                };
            } else {
                return {
                    success: false,
                    message: result.message || '注册失败'
                };
            }
        } catch (error) {
            console.error('注册失败:', error);
            return {
                success: false,
                message: '网络错误，请稍后重试'
            };
        }
    }

    /**
     * 获取会员信息
     * @param {string} userName - 用户名
     * @returns {Promise<Object>} 会员信息
     */
    async getMemberInfo(userName) {
        try {
            const response = await fetch(`${this.baseUrl}/info?userName=${encodeURIComponent(userName)}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();
            
            if (result.code === 200) {
                return {
                    success: true,
                    data: result.data
                };
            } else {
                return {
                    success: false,
                    message: result.message || '获取会员信息失败'
                };
            }
        } catch (error) {
            console.error('获取会员信息失败:', error);
            return {
                success: false,
                message: '网络错误，请稍后重试'
            };
        }
    }

    /**
     * 退出登录
     * @returns {Promise<Object>} 退出结果
     */
    async logout() {
        try {
            const response = await fetch(`${this.baseUrl}/logout`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            // 清除本地存储的会员信息
            this.currentMember = null;
            localStorage.removeItem(this.tokenKey);
            localStorage.removeItem(this.memberInfoKey);
            
            return {
                success: true,
                message: '退出登录成功'
            };
        } catch (error) {
            console.error('退出登录失败:', error);
            // 即使网络请求失败，也清除本地存储
            this.currentMember = null;
            localStorage.removeItem(this.tokenKey);
            localStorage.removeItem(this.memberInfoKey);
            
            return {
                success: true,
                message: '退出登录成功'
            };
        }
    }

    /**
     * 检查用户名是否存在
     * @param {string} userName - 用户名
     * @returns {Promise<boolean>} 是否存在
     */
    async checkUserNameExists(userName) {
        try {
            const response = await fetch(`${this.baseUrl}/check/username/${encodeURIComponent(userName)}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();
            return result.code === 200 ? result.data : false;
        } catch (error) {
            console.error('检查用户名失败:', error);
            return false;
        }
    }

    /**
     * 检查邮箱是否存在
     * @param {string} email - 邮箱
     * @returns {Promise<boolean>} 是否存在
     */
    async checkEmailExists(email) {
        try {
            const response = await fetch(`${this.baseUrl}/check/email/${encodeURIComponent(email)}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();
            return result.code === 200 ? result.data : false;
        } catch (error) {
            console.error('检查邮箱失败:', error);
            return false;
        }
    }

    /**
     * 检查手机号是否存在
     * @param {string} phone - 手机号
     * @returns {Promise<boolean>} 是否存在
     */
    async checkPhoneExists(phone) {
        try {
            const response = await fetch(`${this.baseUrl}/check/phone/${encodeURIComponent(phone)}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();
            return result.code === 200 ? result.data : false;
        } catch (error) {
            console.error('检查手机号失败:', error);
            return false;
        }
    }

    /**
     * 检查是否已登录
     * @returns {boolean} 是否已登录
     */
    isLoggedIn() {
        const token = localStorage.getItem(this.tokenKey);
        const memberInfo = localStorage.getItem(this.memberInfoKey);
        
        if (token && memberInfo) {
            try {
                this.currentMember = JSON.parse(memberInfo);
                return true;
            } catch (error) {
                console.error('解析会员信息失败:', error);
                this.clearMemberData();
                return false;
            }
        }
        return false;
    }

    /**
     * 获取当前会员信息
     * @returns {Object|null} 会员信息
     */
    getMemberInfo() {
        if (this.isLoggedIn()) {
            return this.currentMember;
        }
        return null;
    }

    /**
     * 清除会员数据
     */
    clearMemberData() {
        this.currentMember = null;
        localStorage.removeItem(this.tokenKey);
        localStorage.removeItem(this.memberInfoKey);
    }

    /**
     * 获取会员ID
     * @returns {number|null} 会员ID
     */
    getMemberId() {
        const memberInfo = this.getMemberInfo();
        return memberInfo ? memberInfo.id : null;
    }

    /**
     * 获取会员用户名
     * @returns {string|null} 用户名
     */
    getUserName() {
        const memberInfo = this.getMemberInfo();
        return memberInfo ? memberInfo.userName : null;
    }

    /**
     * 获取会员昵称
     * @returns {string|null} 昵称
     */
    getNickName() {
        const memberInfo = this.getMemberInfo();
        return memberInfo ? memberInfo.nickName : null;
    }

    /**
     * 获取会员真实姓名
     * @returns {string|null} 真实姓名
     */
    getRealName() {
        const memberInfo = this.getMemberInfo();
        return memberInfo ? memberInfo.realName : null;
    }

    /**
     * 获取会员头像
     * @returns {string|null} 头像URL
     */
    getAvatar() {
        const memberInfo = this.getMemberInfo();
        return memberInfo ? memberInfo.avatar : null;
    }

    /**
     * 获取会员邮箱
     * @returns {string|null} 邮箱
     */
    getEmail() {
        const memberInfo = this.getMemberInfo();
        return memberInfo ? memberInfo.email : null;
    }

    /**
     * 获取会员手机号
     * @returns {string|null} 手机号
     */
    getPhone() {
        const memberInfo = this.getMemberInfo();
        return memberInfo ? memberInfo.phone : null;
    }

    /**
     * 获取会员等级
     * @returns {string} 会员等级
     */
    getMemberLevel() {
        const memberInfo = this.getMemberInfo();
        return memberInfo ? memberInfo.memberLevel : '普通会员';
    }

    /**
     * 获取会员积分
     * @returns {number} 积分
     */
    getPoints() {
        const memberInfo = this.getMemberInfo();
        return memberInfo ? memberInfo.points : 0;
    }

    /**
     * 获取会员余额
     * @returns {string} 余额
     */
    getBalance() {
        const memberInfo = this.getMemberInfo();
        return memberInfo ? memberInfo.balance : '0.00';
    }

    /**
     * 获取累计消费
     * @returns {string} 累计消费
     */
    getTotalConsumption() {
        const memberInfo = this.getMemberInfo();
        return memberInfo ? memberInfo.totalConsumption : '0.00';
    }

    /**
     * 获取累计订单数
     * @returns {number} 累计订单数
     */
    getTotalOrders() {
        const memberInfo = this.getMemberInfo();
        return memberInfo ? memberInfo.totalOrders : 0;
    }
}

// 创建全局会员服务实例
window.memberService = new MemberService();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MemberService;
} 