// 轮播图模块
const Carousel = {
    // 轮播配置
    config: {
        autoPlay: true,
        interval: 3000,
        showDots: true
    },

    // 当前轮播数据
    carousels: [],

    // 初始化轮播
    init() {
        this.setupCarousels();
        this.startAutoPlay();
    },

    // 设置轮播
    setupCarousels() {
        const carouselContainers = document.querySelectorAll('.banner-carousel');
        
        carouselContainers.forEach((container, index) => {
            const carousel = {
                container: container,
                slides: container.querySelectorAll('.carousel-item'),
                dots: container.querySelectorAll('.dot'),
                currentSlide: 0,
                isPlaying: false
            };

            // 绑定圆点事件
            carousel.dots.forEach((dot, dotIndex) => {
                dot.addEventListener('click', () => {
                    this.goToSlide(index, dotIndex);
                });
            });

            // 添加触摸事件
            this.addTouchEvents(carousel, index);

            this.carousels.push(carousel);
        });
    },

    // 切换到指定幻灯片
    goToSlide(carouselIndex, slideIndex) {
        const carousel = this.carousels[carouselIndex];
        if (!carousel) return;

        // 移除当前活动状态
        carousel.slides[carousel.currentSlide].classList.remove('active');
        carousel.dots[carousel.currentSlide].classList.remove('active');

        // 设置新的活动状态
        carousel.currentSlide = slideIndex;
        carousel.slides[carousel.currentSlide].classList.add('active');
        carousel.dots[carousel.currentSlide].classList.add('active');
    },

    // 下一张幻灯片
    nextSlide(carouselIndex) {
        const carousel = this.carousels[carouselIndex];
        if (!carousel) return;

        const nextIndex = (carousel.currentSlide + 1) % carousel.slides.length;
        this.goToSlide(carouselIndex, nextIndex);
    },

    // 上一张幻灯片
    prevSlide(carouselIndex) {
        const carousel = this.carousels[carouselIndex];
        if (!carousel) return;

        const prevIndex = carousel.currentSlide === 0 
            ? carousel.slides.length - 1 
            : carousel.currentSlide - 1;
        this.goToSlide(carouselIndex, prevIndex);
    },

    // 添加触摸事件
    addTouchEvents(carousel, carouselIndex) {
        let startX = 0;
        let currentX = 0;
        let isDragging = false;

        const container = carousel.container;

        container.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            isDragging = true;
            container.style.transition = 'none';
        });

        container.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            
            currentX = e.touches[0].clientX;
            const diffX = currentX - startX;
            
            // 阻止默认滚动
            if (Math.abs(diffX) > 10) {
                e.preventDefault();
            }
        });

        container.addEventListener('touchend', (e) => {
            if (!isDragging) return;
            
            isDragging = false;
            container.style.transition = '';
            
            const diffX = currentX - startX;
            const threshold = 50; // 滑动阈值

            if (Math.abs(diffX) > threshold) {
                if (diffX > 0) {
                    // 向右滑动，显示上一张
                    this.prevSlide(carouselIndex);
                } else {
                    // 向左滑动，显示下一张
                    this.nextSlide(carouselIndex);
                }
            }

            startX = 0;
            currentX = 0;
        });
    },

    // 开始自动播放
    startAutoPlay() {
        if (!this.config.autoPlay) return;

        this.carousels.forEach((carousel, index) => {
            if (carousel.slides.length <= 1) return;

            carousel.timer = setInterval(() => {
                this.nextSlide(index);
            }, this.config.interval);

            carousel.isPlaying = true;
        });
    },

    // 停止自动播放
    stopAutoPlay(carouselIndex) {
        const carousel = this.carousels[carouselIndex];
        if (carousel && carousel.timer) {
            clearInterval(carousel.timer);
            carousel.isPlaying = false;
        }
    },

    // 暂停所有轮播
    pauseAll() {
        this.carousels.forEach((carousel, index) => {
            this.stopAutoPlay(index);
        });
    },

    // 恢复所有轮播
    resumeAll() {
        this.startAutoPlay();
    }
};

// 页面可见性变化时暂停/恢复轮播
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        Carousel.pauseAll();
    } else {
        Carousel.resumeAll();
    }
});

export default Carousel; 