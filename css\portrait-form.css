/* 姻缘画像表单样式 */
.portrait-form {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.form-category {
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    border: 1px solid #e0e0e0;
}

.category-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e74c3c;
}

.category-fields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.field-label {
    font-size: 14px;
    font-weight: 500;
    color: #34495e;
}

/* 范围滑块样式 */
.range-input {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.range-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e74c3c, #f39c12);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.range-input::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.range-value {
    font-size: 12px;
    color: #7f8c8d;
    text-align: center;
    font-weight: 500;
}

/* 下拉选择框样式 */
.select-input {
    padding: 10px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    color: #2c3e50;
    cursor: pointer;
    transition: all 0.3s ease;
    outline: none;
}

.select-input:focus {
    border-color: #e74c3c;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.select-input:hover {
    border-color: #f39c12;
}

/* 多选框容器样式 */
.multi-select-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.checkbox-label:hover {
    background: #f8f9fa;
    border-color: #e74c3c;
    transform: translateY(-1px);
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #e74c3c;
    cursor: pointer;
}

.checkbox-text {
    font-size: 13px;
    color: #2c3e50;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .portrait-form {
        padding: 15px;
        margin: 10px;
    }
    
    .form-category {
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .category-fields {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .multi-select-container {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 8px;
    }
    
    .category-title {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .portrait-form {
        padding: 10px;
        margin: 5px;
    }
    
    .form-category {
        padding: 12px;
    }
    
    .multi-select-container {
        grid-template-columns: 1fr;
    }
    
    .checkbox-label {
        padding: 6px 10px;
    }
}

/* 动画效果 */
.form-category {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 表单提交按钮样式 */
.form-submit {
    text-align: center;
    margin-top: 30px;
    padding: 20px;
}

.submit-btn {
    background: linear-gradient(135deg, #e74c3c, #f39c12);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.submit-btn:active {
    transform: translateY(0);
}

/* 加载状态 */
.form-loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
} 