/**
 * 用户状态管理模块
 * 提供全局的用户登录状态管理和事件通知
 */

class UserStateManager {
    constructor() {
        this.currentUser = null;
        this.isLoggedIn = false;
        this.userType = null;
        this.listeners = new Set();
        this.init();
    }

    /**
     * 初始化
     */
    init() {
        console.log('用户状态管理器初始化');
        
        // 设置全局事件监听
        this.setupGlobalListeners();
        
        // 延迟检查初始登录状态，确保其他服务已初始化
        setTimeout(() => {
            this.checkInitialState();
        }, 100);
    }

    /**
     * 检查初始状态
     */
    checkInitialState() {
        console.log('检查初始登录状态...');
        
        // 检查统一认证服务
        if (window.unifiedAuthService) {
            const isLoggedIn = window.unifiedAuthService.isLoggedIn();
            const userType = window.unifiedAuthService.getUserType();
            const currentUser = window.unifiedAuthService.getCurrentUser();
            
            console.log('统一认证服务状态:', {
                isLoggedIn,
                userType,
                hasCurrentUser: !!currentUser
            });
            
            if (isLoggedIn && currentUser) {
                this.userType = userType;
                this.currentUser = currentUser;
                this.isLoggedIn = true;
                
                console.log('检测到已登录用户:', this.currentUser);
                this.notifyStateChange('login', this.currentUser);
            } else if (isLoggedIn && !currentUser) {
                // 有token但没有用户信息，尝试获取用户信息
                console.log('检测到token但缺少用户信息，尝试获取...');
                this.attemptGetUserInfo();
            }
        } else if (window.memberService && window.memberService.isLoggedIn()) {
            this.userType = 'member';
            this.currentUser = window.memberService.getMemberInfo();
            this.isLoggedIn = !!this.currentUser;
            
            if (this.isLoggedIn) {
                console.log('检测到已登录会员:', this.currentUser);
                this.notifyStateChange('login', this.currentUser);
            }
        }
    }

    /**
     * 尝试获取用户信息
     */
    async attemptGetUserInfo() {
        if (!window.unifiedAuthService) return;
        
        try {
            console.log('尝试获取用户信息...');
            const result = await window.unifiedAuthService.getUserInfo();
            
            if (result.success && result.data) {
                this.currentUser = result.data;
                this.userType = window.unifiedAuthService.getUserType();
                this.isLoggedIn = true;
                
                console.log('成功获取用户信息:', this.currentUser);
                this.notifyStateChange('login', this.currentUser);
            } else {
                console.log('获取用户信息失败:', result.message);
                // 如果获取用户信息失败，清除无效的登录状态
                this.clearInvalidLoginState();
            }
        } catch (error) {
            console.error('获取用户信息出错:', error);
            this.clearInvalidLoginState();
        }
    }

    /**
     * 清除无效的登录状态
     */
    clearInvalidLoginState() {
        console.log('清除无效的登录状态');
        if (window.unifiedAuthService) {
            window.unifiedAuthService.clearAuth();
        }
        this.currentUser = null;
        this.userType = null;
        this.isLoggedIn = false;
    }

    /**
     * 设置全局事件监听
     */
    setupGlobalListeners() {
        // 监听登录成功事件
        window.addEventListener('userLoginSuccess', (event) => {
            console.log('用户状态管理器收到登录成功事件');
            this.handleLoginSuccess(event.detail);
        });

        // 监听退出登录事件
        window.addEventListener('userLogout', (event) => {
            console.log('用户状态管理器收到退出登录事件');
            this.handleLogout();
        });

        // 监听用户信息更新事件
        window.addEventListener('userInfoUpdate', (event) => {
            console.log('用户状态管理器收到用户信息更新事件');
            this.handleUserInfoUpdate(event.detail);
        });
    }

    /**
     * 处理登录成功
     */
    handleLoginSuccess(detail) {
        this.currentUser = detail.userInfo;
        this.userType = detail.userType;
        this.isLoggedIn = true;
        
        console.log('用户状态管理器更新登录状态:', this.currentUser);
        this.notifyStateChange('login', this.currentUser);
    }

    /**
     * 处理退出登录
     */
    handleLogout() {
        this.currentUser = null;
        this.userType = null;
        this.isLoggedIn = false;
        
        console.log('用户状态管理器更新退出状态');
        this.notifyStateChange('logout');
    }

    /**
     * 处理用户信息更新
     */
    handleUserInfoUpdate(detail) {
        this.currentUser = detail.userInfo;
        
        console.log('用户状态管理器更新用户信息:', this.currentUser);
        this.notifyStateChange('update', this.currentUser);
    }

    /**
     * 获取当前用户
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * 获取登录状态
     */
    getLoginStatus() {
        return this.isLoggedIn;
    }

    /**
     * 获取用户类型
     */
    getUserType() {
        return this.userType;
    }

    /**
     * 添加状态变化监听器
     */
    addStateListener(callback) {
        this.listeners.add(callback);
        return () => this.listeners.delete(callback); // 返回移除函数
    }

    /**
     * 通知状态变化
     */
    notifyStateChange(type, userInfo = null) {
        const event = {
            type: type,
            userInfo: userInfo,
            isLoggedIn: this.isLoggedIn,
            userType: this.userType,
            timestamp: Date.now()
        };

        console.log('通知状态变化:', event);
        
        // 通知所有监听器
        this.listeners.forEach(callback => {
            try {
                callback(event);
            } catch (error) {
                console.error('状态监听器执行错误:', error);
            }
        });

        // 触发全局事件
        const globalEvent = new CustomEvent('userStateChange', {
            detail: event
        });
        window.dispatchEvent(globalEvent);
    }

    /**
     * 强制刷新用户信息
     */
    async refreshUserInfo() {
        if (!this.isLoggedIn) {
            console.warn('用户未登录，无法刷新用户信息');
            return false;
        }

        try {
            if (window.unifiedAuthService) {
                const result = await window.unifiedAuthService.getUserInfo();
                if (result.success) {
                    this.currentUser = result.data;
                    this.notifyStateChange('update', this.currentUser);
                    return true;
                }
            }
        } catch (error) {
            console.error('刷新用户信息失败:', error);
        }

        return false;
    }

    /**
     * 检查用户权限
     */
    hasPermission(permission) {
        if (!this.isLoggedIn || !this.currentUser) {
            return false;
        }

        // 这里可以根据实际需求实现权限检查逻辑
        // 例如检查用户角色、权限列表等
        return true; // 暂时返回true
    }

    /**
     * 检查用户角色
     */
    hasRole(role) {
        if (!this.isLoggedIn || !this.currentUser) {
            return false;
        }

        // 这里可以根据实际需求实现角色检查逻辑
        return this.userType === role;
    }
}

// 创建全局实例
window.userStateManager = new UserStateManager(); 