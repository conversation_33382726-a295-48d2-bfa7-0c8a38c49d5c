// 宝宝起名模块
console.log('baby-name.js 文件已加载');

class BabyNameModule {
    constructor() {
        this.modal = null;
        this.canvas = null;
        this.ctx = null;
        this.animationId = null;
        this.particles = [];
        this.toys = [];
        this.init();
        // 初始化认证服务
        if (window.initializeAuthServices) {
            window.initializeAuthServices();
        }
    }

    init() {
        this.modal = document.getElementById('babyNameModal');
        this.canvas = document.getElementById('babyNameCanvas');
        
        if (this.canvas) {
            this.ctx = this.canvas.getContext('2d');
            // 暂时不启动动画，等到模态框打开时再启动
        }

        this.bindEvents();
        this.initForm();
    }

    // 初始化Canvas
    initCanvas() {
        const resizeCanvas = () => {
            const rect = this.canvas.parentElement.getBoundingClientRect();
            this.canvas.width = rect.width;
            this.canvas.height = rect.height;
        };
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
    }

    // 创建粒子
    createParticles() {
        this.particles = [];
        const particleCount = 15;
        
        for (let i = 0; i < particleCount; i++) {
            this.particles.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                size: Math.random() * 8 + 3,
                speedX: (Math.random() - 0.5) * 0.8,
                speedY: (Math.random() - 0.5) * 0.8,
                opacity: Math.random() * 0.6 + 0.2,
                symbol: this.getRandomBabySymbol(),
                color: this.getRandomBabyColor(),
                rotation: Math.random() * Math.PI * 2,
                rotationSpeed: (Math.random() - 0.5) * 0.02
            });
        }
    }

    // 获取随机婴儿符号
    getRandomBabySymbol() {
        const symbols = ['🍼', '🧸', '👶', '🎀', '🌟', '💖', '🎁', '🌙', '⭐', '💝'];
        return symbols[Math.floor(Math.random() * symbols.length)];
    }

    // 获取随机婴儿颜色
    getRandomBabyColor() {
        const colors = [
            'rgba(255, 182, 193, ',  // 浅粉色
            'rgba(255, 192, 203, ',  // 粉红色
            'rgba(255, 105, 180, ',  // 热粉色
            'rgba(255, 160, 122, ',  // 浅橙色
            'rgba(221, 160, 221, '   // 梅红色
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    // 创建玩具装饰
    createToys() {
        this.toys = [
            {
                x: this.canvas.width * 0.1,
                y: this.canvas.height * 0.2,
                symbol: '🧸',
                size: 25,
                float: 0,
                floatSpeed: 0.01
            },
            {
                x: this.canvas.width * 0.9,
                y: this.canvas.height * 0.3,
                symbol: '🎀',
                size: 20,
                float: Math.PI,
                floatSpeed: 0.015
            },
            {
                x: this.canvas.width * 0.2,
                y: this.canvas.height * 0.8,
                symbol: '🍼',
                size: 22,
                float: Math.PI / 2,
                floatSpeed: 0.012
            }
        ];
    }

    // 绘制粒子
    drawParticle(particle) {
        this.ctx.save();
        this.ctx.translate(particle.x, particle.y);
        this.ctx.rotate(particle.rotation);
        this.ctx.globalAlpha = particle.opacity;
        this.ctx.font = `${particle.size}px Arial`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(particle.symbol, 0, 0);
        this.ctx.restore();
    }

    // 绘制玩具
    drawToy(toy) {
        this.ctx.save();
        this.ctx.translate(toy.x, toy.y + Math.sin(toy.float) * 5);
        this.ctx.font = `${toy.size}px Arial`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.globalAlpha = 0.3;
        this.ctx.fillText(toy.symbol, 0, 0);
        this.ctx.restore();
    }

    // 绘制彩虹
    drawRainbow() {
        // 确保canvas尺寸有效
        if (this.canvas.width <= 0 || this.canvas.height <= 0) {
            return;
        }
        
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height * 0.15;
        const baseRadius = Math.max(this.canvas.width * 0.3, 50); // 确保最小半径
        
        const colors = ['#FF69B4', '#FFB6C1', '#FFC0CB', '#FFE4E1'];
        
        this.ctx.save();
        this.ctx.globalAlpha = 0.2;
        
        for (let i = 0; i < colors.length; i++) {
            const radius = baseRadius - i * 8;
            // 确保半径不为负数
            if (radius > 0) {
                this.ctx.beginPath();
                this.ctx.arc(centerX, centerY, radius, 0, Math.PI);
                this.ctx.strokeStyle = colors[i];
                this.ctx.lineWidth = 6;
                this.ctx.stroke();
            }
        }
        
        this.ctx.restore();
    }

    // 动画循环
    animate() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制彩虹背景
        this.drawRainbow();

        // 更新和绘制粒子
        this.particles.forEach(particle => {
            particle.x += particle.speedX;
            particle.y += particle.speedY;
            particle.rotation += particle.rotationSpeed;

            // 边界检查
            if (particle.x < -particle.size) particle.x = this.canvas.width + particle.size;
            if (particle.x > this.canvas.width + particle.size) particle.x = -particle.size;
            if (particle.y < -particle.size) particle.y = this.canvas.height + particle.size;
            if (particle.y > this.canvas.height + particle.size) particle.y = -particle.size;

            this.drawParticle(particle);
        });

        // 更新和绘制玩具
        this.toys.forEach(toy => {
            toy.float += toy.floatSpeed;
            this.drawToy(toy);
        });

        this.animationId = requestAnimationFrame(() => this.animate());
    }

    // 初始化表单
    initForm() {
        const form = document.getElementById('babyNameForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmit();
            });
        }
    }

    // 处理表单提交
    handleFormSubmit() {
        const formData = new FormData(document.getElementById('babyNameForm'));
        
        // 获取表单数据
        const surname = formData.get('parentSurname');
        const gender = formData.get('babyGender');
        const birthDate = formData.get('babyBirthDate');
        const birthHour = formData.get('babyBirthHour');
        const avoidChars = formData.get('avoidChars');
        
        // 获取起名风格偏好
        const nameStyles = [];
        const styleCheckboxes = formData.getAll('nameStyle');
        styleCheckboxes.forEach(style => {
            nameStyles.push(style);
        });

        // 验证必填字段
        if (!surname || !gender || !birthDate || !birthHour) {
            alert('请填写宝宝的基本信息（姓氏、性别、出生日期和时辰）');
            return;
        }

        // 解析出生日期
        const birth = new Date(birthDate);
        const year = birth.getFullYear();
        const month = birth.getMonth() + 1;
        const day = birth.getDate();

        // 获取时辰名称
        const getHourName = (hourValue) => {
            const hourMap = {
                'zi': '子时', 'chou': '丑时', 'yin': '寅时', 'mao': '卯时',
                'chen': '辰时', 'si': '巳时', 'wu': '午时', 'wei': '未时',
                'shen': '申时', 'you': '酉时', 'xu': '戌时', 'hai': '亥时'
            };
            return hourMap[hourValue] || hourValue;
        };

        // 获取性别描述
        const getGenderName = (genderValue) => {
            const genderMap = {
                'male': '男宝宝',
                'female': '女宝宝',
                'unknown': '性别未知'
            };
            return genderMap[genderValue] || genderValue;
        };

        // 生成示例好名字（模拟）
        const generateSampleNames = (surname, gender) => {
            const maleNames = ['浩然', '子轩', '天佑', '文昊', '俊驰', '瑞霖'];
            const femaleNames = ['雨萱', '梦琪', '语嫣', '诗涵', '婉儿', '思怡'];
            const unisexNames = ['子涵', '雨泽', '晨曦', '悦心', '嘉慧', '思远'];
            
            let namePool = [];
            if (gender === 'male') {
                namePool = maleNames;
            } else if (gender === 'female') {
                namePool = femaleNames;
            } else {
                namePool = unisexNames;
            }
            
            const selectedNames = [];
            for (let i = 0; i < 3; i++) {
                const randomName = namePool[Math.floor(Math.random() * namePool.length)];
                selectedNames.push(surname + randomName);
            }
            return selectedNames;
        };

        const sampleNames = generateSampleNames(surname, gender);
        const styleText = nameStyles.length > 0 ? `（偏好：${nameStyles.join('、')}）` : '';
        const avoidText = avoidChars ? `\n避免用字：${avoidChars}` : '';

        console.log('宝宝起名数据:', {
            surname, gender, birthDate, birthHour, nameStyles, avoidChars
        });

        alert(`👶 宝宝起名信息已收到：\n\n宝宝信息：${getGenderName(gender)}\n姓氏：${surname}\n生辰：${year}年${month}月${day}日 ${getHourName(birthHour)}\n起名风格：${styleText}\n${avoidText}\n\n精选好名推荐：\n${sampleNames.join('\n')}\n\n🎁 专业大师将为您精心挑选100个吉祥好名，详细的起名报告将在10-15分钟内完成。\n\n💝 愿宝宝健康成长，名字伴随一生好运！`);

        this.closeModal();
    }

    // 绑定事件
    bindEvents() {
        // 绑定服务项点击事件
        document.addEventListener('click', (e) => {
            const serviceItem = e.target.closest('[data-service="baby-name"]');
            if (serviceItem) {
                console.log('🍼 宝宝起名按钮被点击了！跳转到独立页面...');
                // 跳转到独立页面而不是打开模态框
                window.location.href = 'pages/baby-naming/index.html';
            }
        });

        // 点击遮罩关闭模态框
        if (this.modal) {
            this.modal.addEventListener('click', (e) => {
                if (e.target === this.modal) {
                    this.closeModal();
                }
            });
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('show')) {
                this.closeModal();
            }
        });
    }

    // 打开模态框
    openModal() {
        console.log('💕 正在打开宝宝起名界面...');
        if (this.modal) {
            this.modal.classList.add('show');
            this.modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            
            // 重新初始化Canvas
            setTimeout(() => {
                if (this.canvas && this.ctx) {
                    this.initCanvas();
                    this.createParticles();
                    this.createToys();
                    // 启动动画
                    if (!this.animationId) {
                        this.animate();
                    }
                    console.log('🌈 宝宝起名界面已打开，动画已启动');
                }
            }, 100);
        } else {
            console.error('❌ 宝宝起名模态框元素未找到！');
        }
    }

    // 关闭模态框
    closeModal() {
        if (this.modal) {
            this.modal.classList.remove('show');
            setTimeout(() => {
                this.modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }, 300);
            
            // 停止动画以节省资源
            if (this.animationId) {
                cancelAnimationFrame(this.animationId);
                this.animationId = null;
                console.log('👋 宝宝起名界面已关闭');
            }
        }
    }

    // 销毁动画
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
    }
}

// 全局函数供HTML调用
function closeBabyNameModal() {
    if (window.babyNameModule) {
        window.babyNameModule.closeModal();
    }
}

// 初始化宝宝起名模块
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 正在初始化宝宝起名模块...');
    
    // 检查宝宝起名服务卡片是否存在
    const babyNameService = document.querySelector('[data-service="baby-name"]');
    if (babyNameService) {
        console.log('✅ 找到宝宝起名服务卡片');
        window.babyNameModule = new BabyNameModule();
        console.log('🎉 宝宝起名模块初始化完成！');
    } else {
        console.error('❌ 未找到宝宝起名服务卡片！');
    }
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (window.babyNameModule) {
        window.babyNameModule.destroy();
    }
});

// 宝宝起名AI系统
class BabyNameAI {
    constructor() {
        this.initializeDatabase();
        this.isGenerating = false;
        this.generatedNames = [];
    }

    // 初始化起名数据库
    initializeDatabase() {
        this.nameDatabase = {
            // 五行属性字典
            wuxing: {
                metal: ['锐', '铁', '钢', '银', '金', '铭', '锋', '钊', '铜', '镇', '钧', '钦', '锡', '钰', '钻', '铮', '锦', '铭', '钲', '钧', '钟', '锐', '银'],
                wood: ['林', '森', '木', '松', '柏', '杨', '柳', '桃', '李', '梅', '竹', '茗', '茶', '花', '草', '菊', '兰', '莲', '荷', '芬', '芳', '芸', '苗', '若'],
                water: ['海', '江', '河', '湖', '泉', '雨', '雪', '冰', '霜', '露', '云', '雾', '波', '涛', '流', '清', '净', '洁', '润', '泽', '沁', '溪', '源', '淳'],
                fire: ['阳', '光', '明', '亮', '晶', '星', '炎', '热', '焰', '煌', '耀', '照', '晓', '曦', '昊', '晨', '旭', '灿', '烁', '熠', '炫', '燃', '烨', '炯'],
                earth: ['山', '石', '土', '岩', '峰', '岭', '丘', '原', '野', '田', '地', '坤', '城', '墙', '堂', '坊', '圣', '坚', '均', '坦', '垚', '培', '塘', '境']
            },

            // 常用好字典
            goodChars: {
                male: {
                    classic: ['文', '武', '志', '成', '豪', '杰', '俊', '英', '雄', '伟', '强', '勇', '智', '贤', '德', '仁', '义', '礼', '信', '诚', '正', '刚', '毅', '恒'],
                    modern: ['浩', '宇', '轩', '泽', '涵', '博', '睿', '晨', '阳', '超', '凯', '辉', '鹏', '飞', '达', '峰', '磊', '鑫', '宸', '昊', '煜', '琪', '瑞', '祥'],
                    literary: ['诗', '书', '墨', '韵', '雅', '逸', '清', '远', '深', '渊', '鸿', '儒', '学', '问', '知', '慧', '思', '悟', '理', '道', '禅', '静', '和', '谐'],
                    auspicious: ['福', '禄', '寿', '喜', '财', '运', '康', '安', '泰', '顺', '利', '吉', '祥', '瑞', '宝', '贵', '富', '荣', '华', '昌', '盛', '兴', '旺', '发']
                },
                female: {
                    classic: ['婉', '淑', '贤', '惠', '慧', '雅', '静', '娴', '秀', '美', '丽', '娟', '姝', '妍', '嫣', '颖', '聪', '敏', '巧', '灵', '玉', '珠', '琳', '瑶'],
                    modern: ['萱', '涵', '晗', '悦', '欣', '怡', '嘉', '琪', '琳', '瑶', '娜', '莉', '薇', '蕾', '曼', '妮', '思', '语', '梦', '馨', '洁', '雯', '彤', '妤'],
                    literary: ['诗', '词', '画', '琴', '棋', '书', '墨', '兰', '菊', '梅', '竹', '荷', '莲', '芸', '若', '冰', '雪', '露', '霜', '云', '月', '星', '辰', '晓'],
                    auspicious: ['福', '禄', '寿', '喜', '安', '康', '顺', '吉', '祥', '瑞', '宝', '贵', '华', '荣', '昌', '盛', '兴', '旺', '欣', '悦', '乐', '欢', '喜', '庆']
                }
            },

            // 生辰八字时辰对应
            timeMapping: {
                zi: { name: '子时', hour: [23, 1], element: 'water' },
                chou: { name: '丑时', hour: [1, 3], element: 'earth' },
                yin: { name: '寅时', hour: [3, 5], element: 'wood' },
                mao: { name: '卯时', hour: [5, 7], element: 'wood' },
                chen: { name: '辰时', hour: [7, 9], element: 'earth' },
                si: { name: '巳时', hour: [9, 11], element: 'fire' },
                wu: { name: '午时', hour: [11, 13], element: 'fire' },
                wei: { name: '未时', hour: [13, 15], element: 'earth' },
                shen: { name: '申时', hour: [15, 17], element: 'metal' },
                you: { name: '酉时', hour: [17, 19], element: 'metal' },
                xu: { name: '戌时', hour: [19, 21], element: 'earth' },
                hai: { name: '亥时', hour: [21, 23], element: 'water' }
            },

            // 五行相生相克
            wuxingRelation: {
                generation: { // 相生
                    wood: 'fire',
                    fire: 'earth', 
                    earth: 'metal',
                    metal: 'water',
                    water: 'wood'
                },
                restriction: { // 相克
                    wood: 'earth',
                    earth: 'water',
                    water: 'fire',
                    fire: 'metal',
                    metal: 'wood'
                }
            },

            // 名字寓意库
            meanings: {
                '浩': '广大、浩瀚，寓意心胸宽广，前程似海',
                '然': '自然、坦然，寓意性格纯真，生活自在',
                '轩': '高雅、轩昂，寓意气宇轩昂，志向高远',
                '涵': '包容、涵养，寓意学识渊博，品德高尚',
                '博': '博学、博大，寓意知识丰富，胸怀宽广',
                '睿': '聪明、睿智，寓意智慧超群，洞察力强',
                '晨': '早晨、朝气，寓意朝气蓬勃，充满希望',
                '瑞': '吉祥、瑞气，寓意吉祥如意，福气满满',
                '雨': '甘露、恩泽，寓意滋润万物，惠及众生',
                '萱': '忘忧草，寓意快乐无忧，生活美好',
                '梦': '梦想、理想，寓意追求梦想，永不放弃',
                '琪': '美玉、珍宝，寓意品德高洁，珍贵难得',
                '思': '思考、思维，寓意思维敏捷，善于思考',
                '语': '言语、表达，寓意口才出众，善于沟通',
                '嫣': '美好、鲜艳，寓意美丽动人，光彩照人',
                '颖': '聪颖、机敏，寓意聪明伶俐，才华出众'
            }
        };
    }

    // 分析生辰八字
    analyzeBazi(birthDate, birthHour) {
        const date = new Date(birthDate);
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        
        // 获取时辰信息
        const hourInfo = this.nameDatabase.timeMapping[birthHour];
        
        // 简化的五行分析（实际应用中需要更复杂的算法）
        const yearElement = this.getYearElement(year);
        const monthElement = this.getMonthElement(month);
        const dayElement = this.getDayElement(day);
        const hourElement = hourInfo ? hourInfo.element : 'earth';
        
        // 统计五行分布
        const elements = [yearElement, monthElement, dayElement, hourElement];
        const elementCount = {
            metal: 0, wood: 0, water: 0, fire: 0, earth: 0
        };
        
        elements.forEach(element => {
            elementCount[element]++;
        });
        
        // 找出缺失或薄弱的五行
        const weakElements = [];
        const strongElements = [];
        
        Object.keys(elementCount).forEach(element => {
            if (elementCount[element] === 0) {
                weakElements.push(element);
            } else if (elementCount[element] >= 2) {
                strongElements.push(element);
            }
        });
        
        // 确定需要补足的五行
        let needElements = weakElements.length > 0 ? weakElements : [];
        if (needElements.length === 0) {
            // 如果没有缺失，选择相对较弱的五行
            const minCount = Math.min(...Object.values(elementCount));
            needElements = Object.keys(elementCount).filter(
                element => elementCount[element] === minCount
            );
        }
        
        return {
            year: { value: year, element: yearElement },
            month: { value: month, element: monthElement },
            day: { value: day, element: dayElement },
            hour: { name: hourInfo?.name || birthHour, element: hourElement },
            elementCount,
            needElements,
            strongElements,
            analysis: this.generateBaziAnalysis(elementCount, needElements, strongElements)
        };
    }

    // 获取年份对应五行（简化算法）
    getYearElement(year) {
        const elements = ['metal', 'water', 'wood', 'fire', 'earth'];
        return elements[year % 5];
    }

    // 获取月份对应五行
    getMonthElement(month) {
        const monthElements = {
            1: 'water', 2: 'water', 3: 'wood', 4: 'wood',
            5: 'fire', 6: 'fire', 7: 'earth', 8: 'earth',
            9: 'metal', 10: 'metal', 11: 'water', 12: 'water'
        };
        return monthElements[month] || 'earth';
    }

    // 获取日期对应五行（简化算法）
    getDayElement(day) {
        const elements = ['metal', 'water', 'wood', 'fire', 'earth'];
        return elements[day % 5];
    }

    // 生成八字分析
    generateBaziAnalysis(elementCount, needElements, strongElements) {
        let analysis = '根据生辰八字分析：\n\n';
        
        // 五行分布分析
        analysis += '五行分布：\n';
        const elementNames = {
            metal: '金', wood: '木', water: '水', fire: '火', earth: '土'
        };
        
        Object.keys(elementCount).forEach(element => {
            const count = elementCount[element];
            const strength = count === 0 ? '缺失' : count === 1 ? '平衡' : '偏强';
            analysis += `${elementNames[element]}：${count}个 (${strength})\n`;
        });
        
        // 补强建议
        if (needElements.length > 0) {
            analysis += `\n建议补强：${needElements.map(e => elementNames[e]).join('、')}\n`;
            analysis += '起名时宜选用相应五行属性的字，有助于平衡命理。';
        } else {
            analysis += '\n五行相对平衡，起名时注意保持和谐即可。';
        }
        
        return analysis;
    }

    // 生成AI起名提示词
    generateAIPrompt(formData, baziAnalysis) {
        const { surname, gender, nameStyles, avoidChars } = formData;
        const needElements = baziAnalysis.needElements;
        const elementNames = {
            metal: '金', wood: '木', water: '水', fire: '火', earth: '土'
        };
        
        let prompt = `请为宝宝起名。

宝宝信息：
- 姓氏：${surname}
- 性别：${gender === 'male' ? '男孩' : gender === 'female' ? '女孩' : '性别未定'}
- 生辰八字分析：${baziAnalysis.analysis}
- 需要补强的五行：${needElements.map(e => elementNames[e]).join('、')}`;

        if (nameStyles && nameStyles.length > 0) {
            const styleMap = {
                classic: '古典雅致',
                modern: '现代时尚',
                literary: '文艺书香',
                auspicious: '吉祥如意'
            };
            prompt += `\n- 起名风格偏好：${nameStyles.map(s => styleMap[s]).join('、')}`;
        }

        if (avoidChars) {
            prompt += `\n- 避免使用的字：${avoidChars}`;
        }

        prompt += `

请提供10个精心挑选的好名字，要求：
1. 符合性别特征和风格偏好
2. 考虑五行平衡，优先使用需要补强的五行属性字
3. 寓意美好，音律和谐
4. 避免生僻字和不吉利的字
5. 每个名字后面请简要说明：五行属性、寓意内涵、适合理由

请以如下格式输出：
姓名：${surname}XXX
五行：X行X行
寓意：XXXXX
理由：XXXXX

请确保名字具有深厚的文化内涵和美好寓意。`;

        return prompt;
    }

    // 调用DeepSeek API生成名字
    async generateNamesWithAI(formData, baziAnalysis) {
        console.log('开始AI起名分析...');
        
        if (!window.aiService) {
            throw new Error('AI服务未初始化');
        }

        try {
            const prompt = this.generateAIPrompt(formData, baziAnalysis);
            console.log('AI起名提示词:', prompt);
            
            let aiResponse;
            if (window.AI_CONFIG.SERVICE_TYPE === 'deepseek') {
                const systemPrompt = "你是一位精通中华传统文化的起名大师，能够根据生辰八字和五行理论为宝宝起出吉祥美好的名字。请用中文回复，严格按照指定的格式输出名字。";
                aiResponse = await window.aiService.callDeepSeek(prompt, systemPrompt);
            } else if (window.AI_CONFIG.SERVICE_TYPE === 'dify') {
                aiResponse = await window.aiService.callDify(prompt);
            } else {
                throw new Error('不支持的AI服务类型');
            }

            console.log('AI响应:', aiResponse);
            
            // 解析AI响应
            const names = this.parseAINameResponse(aiResponse, formData.surname);
            
            // 如果AI解析失败，使用本地算法补充
            if (names.length < 5) {
                const localNames = this.generateLocalNames(formData, baziAnalysis, 10 - names.length);
                names.push(...localNames);
            }
            
            return names.slice(0, 10); // 限制为10个名字
            
        } catch (error) {
            console.error('AI起名失败:', error);
            // 降级到本地算法
            return this.generateLocalNames(formData, baziAnalysis, 10);
        }
    }

    // 解析AI响应，提取名字信息
    parseAINameResponse(response, surname) {
        const names = [];
        
        try {
            // 分割响应文本，查找名字模式
            const lines = response.split('\n');
            let currentName = null;
            
            for (const line of lines) {
                const trimmedLine = line.trim();
                
                // 匹配姓名行
                if (trimmedLine.startsWith('姓名：') || trimmedLine.includes(surname)) {
                    if (currentName) {
                        names.push(currentName);
                    }
                    
                    const nameMatch = trimmedLine.match(new RegExp(`${surname}([\\u4e00-\\u9fa5]{1,2})`));
                    if (nameMatch) {
                        currentName = {
                            fullName: nameMatch[0],
                            givenName: nameMatch[1],
                            wuxing: '',
                            meaning: '',
                            reason: '',
                            score: 85 + Math.floor(Math.random() * 15)
                        };
                    }
                } else if (currentName) {
                    // 匹配其他属性
                    if (trimmedLine.startsWith('五行：')) {
                        currentName.wuxing = trimmedLine.replace('五行：', '').trim();
                    } else if (trimmedLine.startsWith('寓意：')) {
                        currentName.meaning = trimmedLine.replace('寓意：', '').trim();
                    } else if (trimmedLine.startsWith('理由：')) {
                        currentName.reason = trimmedLine.replace('理由：', '').trim();
                    }
                }
            }
            
            // 添加最后一个名字
            if (currentName) {
                names.push(currentName);
            }
            
        } catch (error) {
            console.error('解析AI响应失败:', error);
        }
        
        return names;
    }

    // 本地起名算法（备用方案）
    generateLocalNames(formData, baziAnalysis, count = 10) {
        const { surname, gender, nameStyles } = formData;
        const needElements = baziAnalysis.needElements;
        const names = [];
        
        // 确定字库
        let charPool = [];
        const genderKey = gender === 'unknown' ? 'male' : gender;
        
        if (nameStyles && nameStyles.length > 0) {
            nameStyles.forEach(style => {
                if (this.nameDatabase.goodChars[genderKey][style]) {
                    charPool.push(...this.nameDatabase.goodChars[genderKey][style]);
                }
            });
        } else {
            // 默认使用所有风格
            Object.values(this.nameDatabase.goodChars[genderKey]).forEach(styleChars => {
                charPool.push(...styleChars);
            });
        }
        
        // 添加五行补强字
        needElements.forEach(element => {
            if (this.nameDatabase.wuxing[element]) {
                charPool.push(...this.nameDatabase.wuxing[element]);
            }
        });
        
        // 去重
        charPool = [...new Set(charPool)];
        
        // 生成名字
        for (let i = 0; i < count && charPool.length > 0; i++) {
            const char1 = charPool[Math.floor(Math.random() * charPool.length)];
            const char2 = charPool[Math.floor(Math.random() * charPool.length)];
            
            // 避免重复字
            const givenName = char1 === char2 ? char1 : char1 + char2;
            const fullName = surname + givenName;
            
            // 检查是否已存在
            if (names.some(name => name.fullName === fullName)) {
                continue;
            }
            
            names.push({
                fullName,
                givenName,
                wuxing: this.getCharWuxing(char1) + ' ' + this.getCharWuxing(char2),
                meaning: this.getCharMeaning(char1, char2),
                reason: `根据生辰八字分析，此名字有助于补强${needElements.map(e => this.getElementName(e)).join('、')}五行，寓意美好。`,
                score: 80 + Math.floor(Math.random() * 20),
                isLocal: true
            });
        }
        
        return names;
    }

    // 获取字的五行属性
    getCharWuxing(char) {
        for (const [element, chars] of Object.entries(this.nameDatabase.wuxing)) {
            if (chars.includes(char)) {
                return this.getElementName(element);
            }
        }
        return '土'; // 默认属土
    }

    // 获取字的寓意
    getCharMeaning(char1, char2) {
        const meaning1 = this.nameDatabase.meanings[char1] || '';
        const meaning2 = this.nameDatabase.meanings[char2] || '';
        
        if (meaning1 && meaning2) {
            return meaning1 + '，' + meaning2;
        } else if (meaning1) {
            return meaning1;
        } else if (meaning2) {
            return meaning2;
        } else {
            return '寓意美好，前程似锦';
        }
    }

    // 获取五行中文名称
    getElementName(element) {
        const names = {
            metal: '金', wood: '木', water: '水', fire: '火', earth: '土'
        };
        return names[element] || element;
    }

    // 完整起名分析流程
    async generateNames(formData) {
        this.isGenerating = true;
        
        try {
            console.log('开始宝宝起名分析:', formData);
            
            // 1. 分析生辰八字
            const baziAnalysis = this.analyzeBazi(formData.birthDate, formData.birthHour);
            console.log('八字分析结果:', baziAnalysis);
            
            // 2. 生成名字（优先使用AI，失败则降级到本地算法）
            let names = [];
            
            if (window.AI_CONFIG && window.AI_CONFIG.SERVICE_TYPE !== 'offline') {
                try {
                    names = await this.generateNamesWithAI(formData, baziAnalysis);
                } catch (error) {
                    console.warn('AI起名失败，使用本地算法:', error);
                    names = this.generateLocalNames(formData, baziAnalysis);
                }
            } else {
                names = this.generateLocalNames(formData, baziAnalysis);
            }
            
            // 3. 生成综合报告
            const report = this.generateNamingReport(formData, baziAnalysis, names);
            
            const result = {
                babyInfo: {
                    surname: formData.surname,
                    gender: formData.gender,
                    birthDate: formData.birthDate,
                    birthHour: formData.birthHour,
                    nameStyles: formData.nameStyles,
                    avoidChars: formData.avoidChars
                },
                baziAnalysis,
                names,
                report,
                generatedAt: new Date().toISOString()
            };
            
            this.generatedNames = names;
            return result;
            
        } catch (error) {
            console.error('起名分析失败:', error);
            throw error;
        } finally {
            this.isGenerating = false;
        }
    }

    // 生成起名报告
    generateNamingReport(formData, baziAnalysis, names) {
        const genderText = formData.gender === 'male' ? '男宝宝' : formData.gender === 'female' ? '女宝宝' : '宝宝';
        
        let report = `🎉 ${formData.surname}姓${genderText}起名报告\n\n`;
        
        // 基本信息
        report += `👶 宝宝信息：\n`;
        report += `- 姓氏：${formData.surname}\n`;
        report += `- 性别：${genderText}\n`;
        report += `- 生辰：${formData.birthDate} ${baziAnalysis.hour.name}\n`;
        
        if (formData.nameStyles && formData.nameStyles.length > 0) {
            const styleMap = {
                classic: '古典雅致',
                modern: '现代时尚', 
                literary: '文艺书香',
                auspicious: '吉祥如意'
            };
            report += `- 起名风格：${formData.nameStyles.map(s => styleMap[s]).join('、')}\n`;
        }
        
        // 八字分析
        report += `\n🔮 ${baziAnalysis.analysis}\n`;
        
        // 起名建议
        report += `\n💡 起名建议：\n`;
        report += `根据宝宝的生辰八字分析，建议选用寓意美好、音律和谐的名字。`;
        if (baziAnalysis.needElements.length > 0) {
            report += `特别推荐使用${baziAnalysis.needElements.map(e => this.getElementName(e)).join('、')}属性的字，有助于平衡五行，增强运势。`;
        }
        
        // 精选名字
        report += `\n\n✨ 精选好名推荐：\n`;
        names.slice(0, 5).forEach((name, index) => {
            report += `${index + 1}. ${name.fullName} (评分：${name.score}分)\n`;
            report += `   五行：${name.wuxing}\n`;
            report += `   寓意：${name.meaning}\n`;
            if (name.reason) {
                report += `   推荐理由：${name.reason}\n`;
            }
            report += `\n`;
        });
        
        report += `💝 祝愿宝宝健康成长，名字伴随一生好运！`;
        
        return report;
    }
}

// 导出模块
window.BabyNameAI = BabyNameAI; 