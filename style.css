/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #D4AF37;
    --secondary-color: #FF6B6B;
    --accent-color: #FF8E53;
    --bg-color: #FAFAFA;
    --bg-light: #FFFFFF;
    --text-primary: #2C3E50;
    --text-secondary: #7F8C8D;
    --border-color: #E1E8ED;
    --shadow: 0 2px 15px rgba(212, 175, 55, 0.2);
    --gradient: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    --border-radius: 8px;
    --transition: all 0.3s ease;
    --red-primary: #E74C3C;
    --gold-primary: #F39C12;
}

body {
    font-family: 'Noto Sans SC', sans-serif;
    background: var(--bg-color);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 107, 107, 0.1) 0%, transparent 50%);
}

/* 容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #D4AF37, #F39C12);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    border-bottom: 3px solid #B8860B;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo h1 {
    font-size: 1.8rem;
    color: #FFFFFF;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 0.2rem;
}

.tagline {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.nav-link.active,
.nav-link:hover {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient);
    transition: var(--transition);
}

.nav-link.active::after,
.nav-link:hover::after {
    width: 100%;
}

/* 主体内容 */
main {
    margin-top: 80px;
}

/* 首页横幅 */
.hero {
    padding: 4rem 0;
    background: linear-gradient(135deg, #FFF8DC 0%, #FFFACD 100%);
    position: relative;
    overflow: hidden;
    border-bottom: 3px solid var(--primary-color);
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.hero-content {
    text-align: center;
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* 章节样式 */
section {
    padding: 4rem 0;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
}

/* 服务卡片网格 */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.service-card {
    background: #FFFFFF;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s;
}

.service-card:hover::before {
    left: 100%;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #FFFEF7, #FFF8DC);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.service-card h4 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.service-card p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

.service-price {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--red-primary);
    display: block;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #E74C3C, #FF6B6B);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.service-rating {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
}

.stars {
    font-size: 1.1rem;
}

.service-rating span:last-child {
    font-size: 0.85rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.service-desc {
    background: linear-gradient(135deg, #27AE60, #2ECC71);
    color: white;
    padding: 0.6rem 1.2rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-top: 1rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    transition: var(--transition);
}

.service-desc:hover {
    background: linear-gradient(135deg, #2ECC71, #27AE60);
    transform: scale(1.05);
}

/* 星座网格 */
.zodiac-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
}

.zodiac-card {
    background: #FFFFFF;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.2rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    box-shadow: 0 3px 12px rgba(0,0,0,0.1);
}

.zodiac-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.25);
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #FFFEF7, #FFF8DC);
}

.zodiac-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
}

.zodiac-name {
    font-weight: 600;
    font-size: 1.1rem;
    display: block;
    margin-bottom: 0.5rem;
}

.zodiac-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
    display: block;
    margin-bottom: 1rem;
}

.zodiac-fortune {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.fortune-score {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--secondary-color);
}

.fortune-trend {
    font-size: 1.2rem;
}

/* 测算功能区 */
.fortune-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.category {
    background: #FFFFFF;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    border-top: 4px solid var(--primary-color);
}

.category h4 {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    text-align: center;
}

.category-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.fortune-btn {
    background: linear-gradient(135deg, #D4AF37, #F39C12);
    color: white;
    border: 2px solid #B8860B;
    border-radius: 25px;
    padding: 0.8rem 1.2rem;
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.fortune-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
    background: linear-gradient(135deg, #F39C12, #E67E22);
}

/* 塔罗占卜 */
.tarot-section {
    background: linear-gradient(135deg, #FFF8DC 0%, #FFFACD 100%);
    border-top: 3px solid var(--primary-color);
}

.tarot-intro {
    text-align: center;
    margin-bottom: 3rem;
}

.tarot-intro p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.shuffle-btn {
    background: linear-gradient(135deg, #E74C3C, #FF6B6B);
    color: white;
    border: 2px solid #C0392B;
    border-radius: 30px;
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.shuffle-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
    background: linear-gradient(135deg, #C0392B, #E74C3C);
}

.tarot-cards {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.tarot-card {
    width: 80px;
    height: 120px;
    background: var(--bg-color);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    position: relative;
    overflow: hidden;
}

.tarot-card::before {
    content: '🔮';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: var(--transition);
}

.tarot-card:hover {
    transform: translateY(-10px) rotateY(180deg);
    box-shadow: var(--shadow);
}

.tarot-card.flipped {
    background: var(--gradient);
    transform: rotateY(180deg);
}

.tarot-card.flipped::before {
    opacity: 0;
}

.tarot-result {
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
}

/* 综合测算功能卡片美化 */
.comprehensive-calculation {
    padding: 25px 15px;
    background: linear-gradient(135deg, #F8F9FA, #E9ECEF);
    position: relative;
    overflow: hidden;
}

.comprehensive-calculation::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%23FF69B4" opacity="0.1"/><circle cx="80" cy="40" r="1.5" fill="%23FFD700" opacity="0.15"/><circle cx="60" cy="80" r="1" fill="%23FF8C00" opacity="0.1"/></svg>');
    pointer-events: none;
}

.calculation-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    max-width: 500px;
    margin: 0 auto;
}

.calc-item {
    background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.85));
    border: 2px solid rgba(255,105,180,0.1);
    border-radius: 20px;
    padding: 20px 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    backdrop-filter: blur(10px);
}

.calc-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.6s ease;
}

.calc-item:hover::before {
    left: 100%;
}

.calc-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 40px rgba(255,105,180,0.25);
    border-color: rgba(255,105,180,0.3);
    background: linear-gradient(135deg, #FFFFFF, rgba(255,182,193,0.1));
}

.calc-icon {
    width: 60px;
    height: 60px;
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    position: relative;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, rgba(255,105,180,0.1), rgba(255,182,193,0.15));
    border: 2px solid rgba(255,105,180,0.2);
}

.calc-item:hover .calc-icon {
    transform: scale(1.1) rotate(5deg);
    background: linear-gradient(135deg, rgba(255,105,180,0.2), rgba(255,182,193,0.25));
    border-color: rgba(255,105,180,0.4);
    box-shadow: 0 8px 20px rgba(255,105,180,0.3);
}

.calc-content {
    text-align: center;
    flex: 1;
}

.calc-content h4 {
    font-size: 16px;
    font-weight: 700;
    color: #2C3E50;
    margin-bottom: 8px;
    line-height: 1.3;
    transition: color 0.3s ease;
}

.calc-item:hover .calc-content h4 {
    color: #FF69B4;
}

.calc-content p {
    font-size: 13px;
    color: #7F8C8D;
    margin-bottom: 12px;
    line-height: 1.4;
    opacity: 0.9;
}

.calc-stats {
    display: flex;
    justify-content: center;
    gap: 12px;
    font-size: 11px;
    margin-bottom: 8px;
}

.calc-stats .views,
.calc-stats .rating {
    background: rgba(255,105,180,0.1);
    color: #FF69B4;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
    border: 1px solid rgba(255,105,180,0.2);
    transition: all 0.3s ease;
}

.calc-item:hover .calc-stats .views,
.calc-item:hover .calc-stats .rating {
    background: rgba(255,105,180,0.2);
    border-color: rgba(255,105,180,0.4);
}

.calc-action {
    background: linear-gradient(135deg, #FF69B4, #FF8FA3);
    color: white;
    border: none;
    border-radius: 18px;
    padding: 12px 20px;
    font-size: 13px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255,105,180,0.3);
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    min-width: 80px;
}

.calc-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255,105,180,0.4);
    background: linear-gradient(135deg, #FF1493, #FF69B4);
}

/* 特殊图标颜色 */
.calc-item:nth-child(1) .calc-icon { background: linear-gradient(135deg, rgba(139,69,19,0.1), rgba(160,82,45,0.15)); }
.calc-item:nth-child(2) .calc-icon { background: linear-gradient(135deg, rgba(255,20,147,0.1), rgba(255,105,180,0.15)); }
.calc-item:nth-child(3) .calc-icon { background: linear-gradient(135deg, rgba(255,215,0,0.1), rgba(255,223,0,0.15)); }
.calc-item:nth-child(4) .calc-icon { background: linear-gradient(135deg, rgba(255,20,147,0.1), rgba(255,105,180,0.15)); }
.calc-item:nth-child(5) .calc-icon { background: linear-gradient(135deg, rgba(0,128,0,0.1), rgba(50,205,50,0.15)); }
.calc-item:nth-child(6) .calc-icon { background: linear-gradient(135deg, rgba(138,43,226,0.1), rgba(147,112,219,0.15)); }
.calc-item:nth-child(7) .calc-icon { background: linear-gradient(135deg, rgba(0,191,255,0.1), rgba(135,206,235,0.15)); }
.calc-item:nth-child(8) .calc-icon { background: linear-gradient(135deg, rgba(255,140,0,0.1), rgba(255,165,0,0.15)); }

/* 移动端适配 */
@media (max-width: 480px) {
    .comprehensive-calculation {
        padding: 20px 10px;
    }
    
    .calculation-list {
        gap: 15px;
        max-width: 100%;
    }
    
    .calc-item {
        padding: 18px 12px;
        border-radius: 16px;
    }
    
    .calc-icon {
        width: 50px;
        height: 50px;
        font-size: 24px;
        border-radius: 14px;
    }
    
    .calc-content h4 {
        font-size: 15px;
        margin-bottom: 6px;
    }
    
    .calc-content p {
        font-size: 12px;
        margin-bottom: 10px;
    }
    
    .calc-action {
        padding: 10px 16px;
        font-size: 12px;
        border-radius: 16px;
    }
}

/* 底部样式 */
.footer {
    background: var(--bg-color);
    border-top: 1px solid var(--border-color);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.footer-section p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.footer-bottom {
    border-top: 2px solid var(--primary-color);
    padding-top: 1.5rem;
    text-align: center;
    color: var(--text-secondary);
    background: linear-gradient(135deg, #FFF8DC, #FFFACD);
    margin: 0 -20px -1rem -20px;
    padding-left: 20px;
    padding-right: 20px;
    padding-bottom: 1.5rem;
}

/* 弹窗样式 */
.modal {
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.modal-content {
    background: var(--bg-light);
    margin: 5% auto;
    padding: 0;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.close {
    color: var(--text-secondary);
    float: right;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 1rem;
    top: 1rem;
    z-index: 1;
    transition: var(--transition);
}

.close:hover {
    color: var(--primary-color);
}

.modal-header {
    background: var(--gradient);
    padding: 2rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-header h3 {
    color: white;
    font-size: 1.5rem;
    margin: 0;
}

.modal-body {
    padding: 2rem;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-color);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.submit-btn {
    background: var(--gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 2rem;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    width: 100%;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.result-content {
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-top: 1rem;
}

.result-item {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.result-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.result-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.result-value {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .header .container {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem 15px;
    }
    
    .nav {
        gap: 0.8rem;
    }
    
    .nav-link {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
    
    .hero-title {
        font-size: 2.2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .hero-stats {
        gap: 1.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        max-width: 500px;
    }
    
    .service-card {
        padding: 1.2rem;
    }
    
    .service-price {
        font-size: 1.6rem;
    }
    
    .zodiac-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
    }
    
    .fortune-categories {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .category-items {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
    
    .tarot-cards {
        gap: 0.5rem;
    }
    
    .tarot-card {
        width: 60px;
        height: 90px;
        font-size: 1.5rem;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    main {
        margin-top: 140px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.8rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .zodiac-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-color);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-color);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(139, 92, 246, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 成功/错误提示 */
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border: 1px solid;
}

.alert-success {
    background-color: rgba(34, 197, 94, 0.1);
    border-color: #22C55E;
    color: #22C55E;
}

.alert-error {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: #EF4444;
    color: #EF4444;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.pulse {
    animation: pulse 2s infinite;
} 
