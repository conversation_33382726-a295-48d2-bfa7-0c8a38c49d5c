/**
 * 年度运势Canvas宣传图生成器 - 美化版
 * 橙金渐变、光晕、星点、排版优化
 */

class YearlyFortuneCanvas {
    constructor(canvasId, options = {}) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        const isMobile = window.innerWidth <= 480;
        this.options = {
            width: this.canvas.width,
            height: this.canvas.height,
            ...options
        };
        this.zodiacAnimals = [
            { name: '鼠', emoji: '🐭', years: [2020, 2032, 2044] },
            { name: '牛', emoji: '🐮', years: [2021, 2033, 2045] },
            { name: '虎', emoji: '🐯', years: [2022, 2034, 2046] },
            { name: '兔', emoji: '🐰', years: [2023, 2035, 2047] },
            { name: '龙', emoji: '🐲', years: [2024, 2036, 2048] },
            { name: '蛇', emoji: '🐍', years: [2025, 2037, 2049] },
            { name: '马', emoji: '🐴', years: [2026, 2038, 2050] },
            { name: '羊', emoji: '🐑', years: [2027, 2039, 2051] },
            { name: '猴', emoji: '🐵', years: [2028, 2040, 2052] },
            { name: '鸡', emoji: '🐔', years: [2029, 2041, 2053] },
            { name: '狗', emoji: '🐶', years: [2030, 2042, 2054] },
            { name: '猪', emoji: '🐷', years: [2031, 2043, 2055] }
        ];
        // 统一橙金色主题
        this.theme = {
            gradient: ['#FFB347', '#FF6B35', '#FFD93D'],
            ring: '#FFD93D',
            text: '#fff',
            main: '#FF6B35',
            accent: '#FFD93D',
        };
        this.init();
    }
    init() {
        this.setupCanvas();
        this.currentYear = new Date().getFullYear();
        this.currentZodiac = this.getZodiacByYear(this.currentYear);
        this.render();
    }
    setupCanvas() {
        const dpr = window.devicePixelRatio || 1;
        this.canvas.width = this.options.width * dpr;
        this.canvas.height = this.options.height * dpr;
        this.canvas.style.width = this.options.width + 'px';
        this.canvas.style.height = this.options.height + 'px';
        this.ctx.setTransform(1, 0, 0, 1, 0, 0);
        this.ctx.scale(dpr, dpr);
    }
    getZodiacByYear(year) {
        return this.zodiacAnimals.find(animal => animal.years.includes(year)) || this.zodiacAnimals[0];
    }
    // 渐变背景+光晕
    drawBackground() {
        const { width, height } = this.options;
        const cx = width / 2, cy = height / 2;
        const grad = this.ctx.createLinearGradient(0, 0, width, height);
        grad.addColorStop(0, this.theme.gradient[0]);
        grad.addColorStop(0.7, this.theme.gradient[1]);
        grad.addColorStop(1, this.theme.gradient[2]);
        this.ctx.fillStyle = grad;
        this.ctx.fillRect(0, 0, width, height);
        // 光晕
        const r = Math.min(width, height) / 2.1;
        const glow = this.ctx.createRadialGradient(cx, cy, r * 0.7, cx, cy, r);
        glow.addColorStop(0, 'rgba(255,255,255,0.18)');
        glow.addColorStop(1, 'rgba(255,255,255,0)');
        this.ctx.beginPath();
        this.ctx.arc(cx, cy, r, 0, Math.PI * 2);
        this.ctx.closePath();
        this.ctx.fillStyle = glow;
        this.ctx.fill();
    }
    // 星点装饰
    drawStars() {
        const { width, height } = this.options;
        for (let i = 0; i < 8; i++) {
            const x = [30, width-30, width-30, 30][i%4] + (Math.random()-0.5)*10;
            const y = [30, 30, height-30, height-30][i%4] + (Math.random()-0.5)*10;
            const size = Math.random() * 2.5 + 1.5;
            this.ctx.save();
            this.ctx.globalAlpha = 0.18 + Math.random()*0.12;
            this.ctx.beginPath();
            this.ctx.arc(x, y, size, 0, Math.PI*2);
            this.ctx.fillStyle = this.theme.accent;
            this.ctx.shadowColor = '#fff';
            this.ctx.shadowBlur = 6;
            this.ctx.fill();
            this.ctx.restore();
        }
    }
    // 圆环+光晕
    drawRing() {
        const { width, height } = this.options;
        const cx = width / 2, cy = height / 2;
        const r = Math.min(width, height) / 3.1;
        // 外环
        this.ctx.save();
        this.ctx.beginPath();
        this.ctx.arc(cx, cy, r, 0, Math.PI * 2);
        this.ctx.strokeStyle = this.theme.ring;
        this.ctx.lineWidth = 8;
        this.ctx.shadowColor = this.theme.ring;
        this.ctx.shadowBlur = 18;
        this.ctx.stroke();
        this.ctx.restore();
        // 内白圈
        this.ctx.save();
        this.ctx.beginPath();
        this.ctx.arc(cx, cy, r-8, 0, Math.PI * 2);
        this.ctx.fillStyle = 'rgba(255,255,255,0.92)';
        this.ctx.shadowColor = '#FFD93D';
        this.ctx.shadowBlur = 6;
        this.ctx.fill();
        this.ctx.restore();
    }
    // 生肖emoji
    drawZodiacIcon() {
        const { width, height } = this.options;
        const cx = width / 2, cy = height / 2;
        this.ctx.font = `${Math.floor(width/6)}px Arial`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.shadowColor = 'rgba(255,107,53,0.18)';
        this.ctx.shadowBlur = 8;
        this.ctx.fillText(this.currentZodiac.emoji, cx, cy+18);
        this.ctx.shadowBlur = 0;
    }
    // 年份徽章
    drawYearBadge() {
        const { width } = this.options;
        const badgeW = 110, badgeH = 28;
        const x = 18, y = 18;
        this.ctx.save();
        this.ctx.beginPath();
        this.ctx.moveTo(x+14, y);
        this.ctx.lineTo(x+badgeW-14, y);
        this.ctx.quadraticCurveTo(x+badgeW, y, x+badgeW, y+14);
        this.ctx.lineTo(x+badgeW, y+badgeH-14);
        this.ctx.quadraticCurveTo(x+badgeW, y+badgeH, x+badgeW-14, y+badgeH);
        this.ctx.lineTo(x+14, y+badgeH);
        this.ctx.quadraticCurveTo(x, y+badgeH, x, y+badgeH-14);
        this.ctx.lineTo(x, y+14);
        this.ctx.quadraticCurveTo(x, y, x+14, y);
        this.ctx.closePath();
        this.ctx.fillStyle = '#fff';
        this.ctx.shadowColor = '#FFD93D';
        this.ctx.shadowBlur = 6;
        this.ctx.fill();
        this.ctx.shadowBlur = 0;
        this.ctx.fillStyle = '#FF6B35';
        this.ctx.font = 'bold 13px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(`${this.currentYear}${this.currentZodiac.name}年开运版`, x+badgeW/2, y+badgeH/2+1);
        this.ctx.restore();
    }
    // 文字排版
    drawTexts() {
        const { width, height } = this.options;
        const cx = width / 2, cy = height / 2;
        // 四方向
        const r = Math.min(width, height) / 2.1;
        const font = width < 400 ? 13 : 15;
        const items = [
            { text: '富贵命财', x: cx, y: cy - r + 38, align: 'center' },
            { text: '桃花姻缘', x: cx + r - 38, y: cy, align: 'left' },
            { text: '事业前程', x: cx, y: cy + r - 38, align: 'center' },
            { text: '安康顺畅', x: cx - r + 38, y: cy, align: 'right' }
        ];
        this.ctx.save();
        this.ctx.font = `bold ${font}px Arial`;
        this.ctx.fillStyle = '#fff';
        this.ctx.shadowColor = 'rgba(255,255,255,0.18)';
        this.ctx.shadowBlur = 2;
        items.forEach(item => {
            this.ctx.textAlign = item.align;
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText(item.text, item.x, item.y);
        });
        this.ctx.restore();
        // 中心
        this.ctx.save();
        this.ctx.font = `bold ${font+3}px Arial`;
        this.ctx.fillStyle = '#FF6B35';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('八字', cx, cy-8);
        this.ctx.font = `${font-1}px Arial`;
        this.ctx.fillStyle = '#FF6B35';
        this.ctx.fillText('流年', cx, cy+10);
        this.ctx.restore();
        // 底部大标题
        this.ctx.save();
        this.ctx.font = `bold ${font+5}px Arial`;
        this.ctx.fillStyle = '#fff';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'bottom';
        this.ctx.shadowColor = 'rgba(255,255,255,0.18)';
        this.ctx.shadowBlur = 2;
        this.ctx.fillText(`${this.currentYear}${this.currentZodiac.name}年运势`, cx, height-18);
        this.ctx.restore();
    }
    render() {
        this.drawBackground();
        this.drawStars();
        this.drawRing();
        this.drawZodiacIcon();
        this.drawYearBadge();
        this.drawTexts();
    }
    updateYear(year) {
        this.currentYear = year;
        this.currentZodiac = this.getZodiacByYear(year);
        this.render();
    }
    toImage() {
        return this.canvas.toDataURL('image/png');
    }
    downloadImage(filename = 'yearly-fortune.png') {
        const link = document.createElement('a');
        link.download = filename;
        link.href = this.toImage();
        link.click();
    }
}
window.YearlyFortuneCanvas = YearlyFortuneCanvas; 