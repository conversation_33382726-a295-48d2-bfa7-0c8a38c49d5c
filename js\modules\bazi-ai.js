/**
 * 八字精批AI分析模块
 * 基于传统八字命理学进行全面分析
 */
class BaziAnalysisAI {
    constructor() {
        this.initializeKnowledgeBase();
    }

    // 初始化八字命理学知识库
    initializeKnowledgeBase() {
        this.baziKnowledge = {
            // 天干配置
            tiangan: {
                jia: { name: '甲', element: '木', type: 'yang', traits: ['主导性强', '有领导才能'] },
                yi: { name: '乙', element: '木', type: 'yin', traits: ['柔韧坚韧', '适应性强'] },
                bing: { name: '丙', element: '火', type: 'yang', traits: ['热情开朗', '富有活力'] },
                ding: { name: '丁', element: '火', type: 'yin', traits: ['内敛温暖', '思维敏捷'] },
                wu: { name: '戊', element: '土', type: 'yang', traits: ['稳重踏实', '可靠诚信'] },
                ji: { name: '己', element: '土', type: 'yin', traits: ['细腻周到', '善于协调'] },
                geng: { name: '庚', element: '金', type: 'yang', traits: ['果断坚毅', '正义感强'] },
                xin: { name: '辛', element: '金', type: 'yin', traits: ['精致敏感', '审美能力强'] },
                ren: { name: '壬', element: '水', type: 'yang', traits: ['智慧灵活', '适应力强'] },
                gui: { name: '癸', element: '水', type: 'yin', traits: ['深邃内敛', '直觉敏锐'] }
            },

            // 地支配置
            dizhi: {
                zi: { name: '子', element: '水', animal: '鼠' },
                chou: { name: '丑', element: '土', animal: '牛' },
                yin: { name: '寅', element: '木', animal: '虎' },
                mao: { name: '卯', element: '木', animal: '兔' },
                chen: { name: '辰', element: '土', animal: '龙' },
                si: { name: '巳', element: '火', animal: '蛇' },
                wu: { name: '午', element: '火', animal: '马' },
                wei: { name: '未', element: '土', animal: '羊' },
                shen: { name: '申', element: '金', animal: '猴' },
                you: { name: '酉', element: '金', animal: '鸡' },
                xu: { name: '戌', element: '土', animal: '狗' },
                hai: { name: '亥', element: '水', animal: '猪' }
            },

            // 五行特征
            wuxing: {
                wood: { name: '木', trait: '仁慈', careers: ['教育', '文化', '医疗'], colors: ['绿色', '青色'] },
                fire: { name: '火', trait: '礼貌', careers: ['娱乐', '广告', '电子'], colors: ['红色', '紫色'] },
                earth: { name: '土', trait: '诚信', careers: ['房地产', '农业', '建筑'], colors: ['黄色', '土色'] },
                metal: { name: '金', trait: '义气', careers: ['金融', '机械', '汽车'], colors: ['白色', '金色'] },
                water: { name: '水', trait: '智慧', careers: ['物流', '旅游', '通讯'], colors: ['黑色', '蓝色'] }
            },

            // 十神配置
            shishen: {
                bijian: { name: '比肩', meaning: '兄弟朋友', traits: ['自立自强', '竞争意识', '团队合作'] },
                jiecai: { name: '劫财', meaning: '争夺竞争', traits: ['冲动行事', '好胜心强', '易有争执'] },
                shishan: { name: '食神', meaning: '才华表达', traits: ['多才多艺', '表达能力强', '享受生活'] },
                shangguang: { name: '伤官', meaning: '创新反叛', traits: ['创新精神', '不拘一格', '容易叛逆'] },
                piancan: { name: '偏财', meaning: '横财机遇', traits: ['投资眼光', '商业头脑', '偏财运好'] },
                zhengcai: { name: '正财', meaning: '正当收入', traits: ['稳定收入', '理财有道', '务实节俭'] },
                qisha: { name: '七杀', meaning: '权威压力', traits: ['权威性格', '承受压力', '执行力强'] },
                zhengguan: { name: '正官', meaning: '地位名声', traits: ['责任感强', '地位崇高', '正直守法'] },
                pianyin: { name: '偏印', meaning: '直觉智慧', traits: ['直觉敏锐', '学习能力强', '思维独特'] },
                zhengyin: { name: '正印', meaning: '知识学问', traits: ['学识渊博', '贵人相助', '母爱关怀'] }
            },

            // 大运流年分析
            dayun: {
                description: '大运是人生运势的十年周期，影响人生各个阶段的发展',
                cycles: [
                    { age: '0-9', phase: '童年期', focus: '健康成长，基础教育' },
                    { age: '10-19', phase: '青少年期', focus: '学习成长，性格形成' },
                    { age: '20-29', phase: '青年期', focus: '事业起步，恋爱结婚' },
                    { age: '30-39', phase: '壮年期', focus: '事业发展，家庭建设' },
                    { age: '40-49', phase: '中年期', focus: '事业巅峰，财富积累' },
                    { age: '50-59', phase: '成熟期', focus: '稳固发展，传承经验' },
                    { age: '60+', phase: '老年期', focus: '享受生活，颐养天年' }
                ]
            },

            // 2025年流年分析
            liunian2025: {
                year: '乙巳年',
                tiangan: '乙木',
                dizhi: '巳火',
                characteristics: '木火通明，利于文化创意和科技发展',
                favorable: ['教育', '文化', '科技', '新能源', '环保'],
                unfavorable: ['传统制造', '重工业', '资源开采']
            }
        };
    }

    // 计算生辰八字（简化算法）
    calculateBazi(birthDate, birthHour) {
        const date = new Date(birthDate);
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();

        // 简化的天干地支计算
        const yearGan = Object.keys(this.baziKnowledge.tiangan)[(year - 4) % 10];
        const yearZhi = Object.keys(this.baziKnowledge.dizhi)[(year - 4) % 12];
        const monthGan = Object.keys(this.baziKnowledge.tiangan)[(month + year) % 10];
        const monthZhi = Object.keys(this.baziKnowledge.dizhi)[(month + 1) % 12];
        const dayGan = Object.keys(this.baziKnowledge.tiangan)[(year * 365 + month * 30 + day) % 10];
        const dayZhi = Object.keys(this.baziKnowledge.dizhi)[(year * 365 + month * 30 + day) % 12];
        
        const hourZhi = birthHour;
        const hourGan = Object.keys(this.baziKnowledge.tiangan)[(Object.keys(this.baziKnowledge.tiangan).indexOf(dayGan) * 2) % 10];

        return {
            year: { gan: yearGan, zhi: yearZhi },
            month: { gan: monthGan, zhi: monthZhi },
            day: { gan: dayGan, zhi: dayZhi },
            hour: { gan: hourGan, zhi: hourZhi },
            dayGan: dayGan
        };
    }

    // 分析五行分布
    analyzeFiveElements(bazi) {
        const elements = { 木: 0, 火: 0, 土: 0, 金: 0, 水: 0 };
        
        // 统计各柱的五行
        Object.values(bazi).forEach(pillar => {
            if (pillar.gan && this.baziKnowledge.tiangan[pillar.gan]) {
                const ganElement = this.baziKnowledge.tiangan[pillar.gan].element;
                elements[ganElement]++;
            }
            if (pillar.zhi && this.baziKnowledge.dizhi[pillar.zhi]) {
                const zhiElement = this.baziKnowledge.dizhi[pillar.zhi].element;
                elements[zhiElement]++;
            }
        });

        const sorted = Object.entries(elements).sort((a, b) => b[1] - a[1]);
        
        return {
            elements,
            strongest: sorted[0][0],
            weakest: sorted[sorted.length - 1][0]
        };
    }

    // 生成AI提示词
    generatePrompt(userData, bazi, fiveElements) {
        const { userName, gender, birthDate, birthHour } = userData;
        const dayGanInfo = this.baziKnowledge.tiangan[bazi.dayGan];
        const genderText = gender === 'male' ? '男性' : '女性';

        return `请为${userName}（${genderText}，生于${birthDate}，${this.baziKnowledge.dizhi[birthHour].name}时）进行八字精批分析。

八字信息：
- 日主：${dayGanInfo.name}${dayGanInfo.element}
- 五行分布：${Object.entries(fiveElements.elements).map(([k,v]) => `${k}${v}个`).join('、')}
- 最强五行：${fiveElements.strongest}
- 最弱五行：${fiveElements.weakest}

请按照以下固定标题格式进行分析，每个部分标题必须严格按照以下格式：

【八字格局分析】
分析日主强弱、五行平衡等格局特点

【性格特征分析】
分析性格优缺点和特征

【事业运势分析】
分析适合的职业方向和事业发展

【财运分析】
分析财运状况和理财建议

【感情婚姻分析】
分析感情状况和婚姻运势

【健康运势分析】
分析健康状况和养生建议

【流年运势分析】
分析2025年运势和机遇

【开运建议】
提供具体的开运方法、颜色、方位等建议

请确保每个部分都有详细内容，并严格按照上述标题格式输出，这样我可以准确提取每个部分的内容。`;
    }

    // AI八字分析
    async analyzeBaziWithAI(userData) {
        try {
            // 计算八字
            const bazi = this.calculateBazi(userData.birthDate, userData.birthHour);
            const fiveElements = this.analyzeFiveElements(bazi);

            if (!window.aiService) {
                return this.generateLocalAnalysis(userData, bazi, fiveElements);
            }

            const prompt = this.generatePrompt(userData, bazi, fiveElements);
            const systemPrompt = "你是一个专业的八字命理分析师，精通传统命理学理论，能够进行准确的八字分析和运势预测。请用中文回复，严格按照指定的格式输出分析结果。";
            const aiResponse = await window.aiService.callDeepSeek(prompt, systemPrompt);
            
            return this.parseAIResponse(aiResponse, userData, bazi, fiveElements);
        } catch (error) {
            console.error('AI分析失败:', error);
            const bazi = this.calculateBazi(userData.birthDate, userData.birthHour);
            const fiveElements = this.analyzeFiveElements(bazi);
            return this.generateLocalAnalysis(userData, bazi, fiveElements);
        }
    }

    // 解析AI响应
    parseAIResponse(aiResponse, userData, bazi, fiveElements) {
        const score = this.calculateBaziScore(bazi, fiveElements);
        const dayGanInfo = this.baziKnowledge.tiangan[bazi.dayGan];

        // 提取各个分析部分
        const sections = this.extractSections(aiResponse);
        
        return {
            success: true,
            baziAnalysis: {
                method: 'ai',
                bazi: bazi,
                fiveElements: fiveElements,
                score: score,
                summary: this.extractSummary(aiResponse, dayGanInfo),
                sections: {
                    pattern: sections.pattern || `八字格局分析：${dayGanInfo.name}${dayGanInfo.element}日主，五行${this.analyzeFiveElementsBalance(fiveElements)}`,
                    character: sections.character || `性格特征：${dayGanInfo.traits.join('、')}`,
                    career: sections.career || `事业运势：适合从事${this.getElementCareers(dayGanInfo.element).join('、')}等行业`,
                    wealth: sections.wealth || `财运分析：稳健发展，把握机遇`,
                    marriage: sections.marriage || `感情婚姻：真诚待人，缘分自来`,
                    health: sections.health || `健康运势：注意${dayGanInfo.element}属性保养，保持规律作息`,
                    timing: sections.timing || `流年运势：2025年乙巳年，木火通明，发展机遇良好`,
                    enhancement: sections.enhancement || `开运建议：使用${this.getLuckyColors(fiveElements.weakest).join('、')}等颜色开运`
                },
                recommendations: this.extractRecommendations(aiResponse, dayGanInfo),
                luckyElements: this.extractLuckyElements(aiResponse, fiveElements),
                timestamp: new Date().toISOString()
            }
        };
    }
    
    // 提取摘要
    extractSummary(content, dayGanInfo) {
        // 尝试提取开头段落作为摘要
        const firstParagraph = content.split(/\n\s*\n/)[0];
        if (firstParagraph && firstParagraph.length > 50) {
            return firstParagraph.substring(0, 200) + '...';
        }
        
        // 如果没有合适的段落，生成默认摘要
        return `您的日主为${dayGanInfo.name}${dayGanInfo.element}，${dayGanInfo.traits[0]}。根据八字分析，您具有${dayGanInfo.traits.join('、')}的特质。在2025年乙巳年，建议您发挥自身优势，把握时机发展。`;
    }
    
    // 提取各个分析部分
    extractSections(content) {
        const sections = {};
        
                 // 定义各部分的匹配模式
         const patterns = {
             pattern: /【八字格局分析】[\s\S]*?(?=【性格特征分析】|$)/i,
             character: /【性格特征分析】[\s\S]*?(?=【事业运势分析】|$)/i,
             career: /【事业运势分析】[\s\S]*?(?=【财运分析】|$)/i,
             wealth: /【财运分析】[\s\S]*?(?=【感情婚姻分析】|$)/i,
             marriage: /【感情婚姻分析】[\s\S]*?(?=【健康运势分析】|$)/i,
             health: /【健康运势分析】[\s\S]*?(?=【流年运势分析】|$)/i,
             timing: /【流年运势分析】[\s\S]*?(?=【开运建议】|$)/i,
             enhancement: /【开运建议】[\s\S]*?(?=$)/i
         };
        
        // 提取各部分内容
        Object.keys(patterns).forEach(key => {
            const match = content.match(patterns[key]);
            if (match && match[0]) {
                // 清理内容，移除标题部分
                let text = match[0];
                const titleMatch = text.match(/【[^】]*】/);
                if (titleMatch) {
                    text = text.substring(titleMatch[0].length);
                }
                sections[key] = text.trim();
            }
        });
        
        return sections;
    }
    
    // 提取建议
    extractRecommendations(content, dayGanInfo) {
        const recommendations = [];
        
                 // 尝试从开运建议部分提取
        const enhancementMatch = content.match(/【开运建议】[\s\S]*?(?=$)/i);
        if (enhancementMatch && enhancementMatch[0]) {
            // 移除标题
            const enhancementText = enhancementMatch[0].replace(/【开运建议】/, '').trim();
            
            // 按行或句号分割
            const lines = enhancementText.split(/[。；;]/);
            lines.forEach(line => {
                if (line.length > 10 && line.length < 100 && 
                    (line.includes('建议') || line.includes('可以') || line.includes('应该') || 
                     line.includes('宜') || line.includes('适合') || line.includes('注意'))) {
                    const cleanLine = line.replace(/^\d+[\.、]/, '').trim();
                    if (cleanLine.length > 5) {
                        recommendations.push(cleanLine);
                    }
                }
            });
        }
        
        // 如果没有提取到足够的建议，添加默认建议
        if (recommendations.length < 3) {
            recommendations.push(
                `发挥${dayGanInfo.traits[0]}的优势`,
                '保持五行平衡发展',
                '2025年把握木火通明的机遇',
                '注意身心健康，规律作息'
            );
        }
        
        return recommendations.slice(0, 6); // 最多返回6条建议
    }
    
    // 提取开运要素
    extractLuckyElements(content, fiveElements) {
        const elements = [];
        
        // 尝试从开运建议部分提取颜色、方位等
        const enhancementMatch = content.match(/【开运建议】[\s\S]*?(?=$)/i);
        if (enhancementMatch && enhancementMatch[0]) {
            // 移除标题
            const enhancementText = enhancementMatch[0].replace(/【开运建议】/, '').trim();
            
            // 提取颜色
            const colorMatches = enhancementText.match(/(红色|绿色|蓝色|黄色|黑色|白色|紫色|橙色|金色|银色)/g);
            if (colorMatches) {
                elements.push(...colorMatches.slice(0, 3));
            }
            
            // 提取方位
            const directionMatches = enhancementText.match(/(东方|南方|西方|北方|东南|西南|东北|西北)/g);
            if (directionMatches) {
                elements.push(...directionMatches.slice(0, 2));
            }
            
            // 提取其他开运要素
            const otherMatches = enhancementText.match(/(佩戴|饰品|水晶|玉石|手链|吊坠|摆件|植物|花卉)/g);
            if (otherMatches) {
                elements.push(...otherMatches.slice(0, 2));
            }
        }
        
        // 添加五行相关的开运要素
        elements.push(
            ...this.getLuckyColors(fiveElements.weakest),
            fiveElements.weakest + '系',
            '平衡五行'
        );
        
        // 去重并限制数量
        return [...new Set(elements)].slice(0, 8);
    }

    // 本地分析
    generateLocalAnalysis(userData, bazi, fiveElements) {
        const dayGanInfo = this.baziKnowledge.tiangan[bazi.dayGan];
        const score = this.calculateBaziScore(bazi, fiveElements);
        
        return {
            success: true,
            baziAnalysis: {
                method: 'local',
                bazi: bazi,
                fiveElements: fiveElements,
                score: score,
                summary: `您的日主为${dayGanInfo.name}${dayGanInfo.element}，${dayGanInfo.traits[0]}。五行分布${this.analyzeFiveElementsBalance(fiveElements)}。`,
                sections: {
                    pattern: `八字日主${dayGanInfo.name}${dayGanInfo.element}，${dayGanInfo.type === 'yang' ? '阳性' : '阴性'}特质，${dayGanInfo.traits.join('、')}。`,
                    character: `性格特征：${dayGanInfo.traits.join('、')}。建议发挥优势，注意平衡发展。`,
                    career: `适合从事${this.getElementCareers(dayGanInfo.element).join('、')}等行业。`,
                    wealth: `财运稳健，建议理性投资，稳步发展。`,
                    marriage: `感情真诚，注重沟通，缘分自然来。`,
                    health: `注意${dayGanInfo.element}属性保养，保持规律作息。`,
                    timing: `2025年乙巳年利于发展，把握机遇。`,
                    enhancement: `建议使用${this.getLuckyColors(fiveElements.weakest).join('、')}等颜色开运。`
                },
                recommendations: [
                    `发挥${dayGanInfo.traits[0]}的优势`,
                    `关注${this.getElementCareers(dayGanInfo.element)[0]}行业`,
                    `使用${this.getLuckyColors(fiveElements.weakest)[0]}开运`,
                    '保持五行平衡，身心健康'
                ],
                luckyElements: this.getLuckyElements(fiveElements),
                timestamp: new Date().toISOString()
            }
        };
    }

    // 计算八字评分
    calculateBaziScore(bazi, fiveElements) {
        const baseScore = 75;
        const balance = this.analyzeFiveElementsBalance(fiveElements);
        let adjustment = 0;
        
        if (balance.includes('平衡')) adjustment += 15;
        else if (balance.includes('较好')) adjustment += 10;
        else adjustment += 5;
        
        return Math.min(Math.max(baseScore + adjustment, 60), 95);
    }

    // 分析五行平衡
    analyzeFiveElementsBalance(fiveElements) {
        const counts = Object.values(fiveElements.elements);
        const max = Math.max(...counts);
        const min = Math.min(...counts);
        
        if (max - min <= 1) return '平衡';
        if (max - min <= 2) return '较好平衡';
        return '需要调理';
    }

    // 获取五行对应职业
    getElementCareers(element) {
        const careerMap = {
            木: ['教育', '文化', '医疗'],
            火: ['娱乐', '广告', '电子'],
            土: ['房地产', '农业', '建筑'],
            金: ['金融', '机械', '汽车'],
            水: ['物流', '旅游', '通讯']
        };
        return careerMap[element] || ['服务业'];
    }

    // 获取幸运颜色
    getLuckyColors(element) {
        const colorMap = {
            木: ['绿色', '青色'],
            火: ['红色', '紫色'],
            土: ['黄色', '土色'],
            金: ['白色', '金色'],
            水: ['黑色', '蓝色']
        };
        return colorMap[element] || ['白色'];
    }

    // 获取开运要素
    getLuckyElements(fiveElements) {
        return [
            ...this.getLuckyColors(fiveElements.weakest),
            fiveElements.weakest + '系',
            '平衡五行',
            '规律作息'
        ];
    }
}

// 导出
window.BaziAnalysisAI = BaziAnalysisAI; 