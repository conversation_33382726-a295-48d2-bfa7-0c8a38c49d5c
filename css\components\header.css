/* 头部导航组件样式 */
.header {
    background: var(--gradient-pink);
    padding: 10px 15px 15px;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 10px rgba(255,105,180,0.3);
    backdrop-filter: blur(10px);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 100%;
    overflow-x: auto;
    white-space: nowrap;
    gap: 15px;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.nav-container::-webkit-scrollbar {
    display: none;
}

.nav-item {
    color: white;
    font-size: 14px;
    font-weight: 500;
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 5px;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.nav-item:hover,
.nav-item:active {
    background: rgba(255,255,255,0.15);
    transform: translateY(-1px);
}

.nav-item.active {
    background: rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(255,255,255,0.2);
}

.nav-item.master-nav {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: white;
    font-weight: 600;
    animation: pulse 2s infinite;
}

.nav-item.hot-nav {
    background: linear-gradient(45deg, #FF4444, #FF6666);
    color: white;
    font-weight: 600;
    position: relative;
    animation: glow 2s ease-in-out infinite alternate;
}

.nav-item.hot-nav::after {
    content: '热';
    position: absolute;
    top: -5px;
    right: -5px;
    background: #FF0000;
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 3px;
    animation: bounce 1s infinite;
}

.logo-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

/* 用户图标样式 */
.user-icon {
    font-size: 16px;
    margin-right: 2px;
}

/* 动画效果 */
@keyframes pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.4);
    }
    50% {
        box-shadow: 0 0 0 8px rgba(255, 215, 0, 0);
    }
}

@keyframes glow {
    from {
        box-shadow: 0 0 5px rgba(255, 68, 68, 0.5);
    }
    to {
        box-shadow: 0 0 15px rgba(255, 68, 68, 0.8);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-3px);
    }
    60% {
        transform: translateY(-1px);
    }
}

/* 子页面头部样式 */
.sub-header {
    background: var(--white);
    border-bottom: 1px solid var(--border-light);
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.sub-header .nav-container {
    background: transparent;
}

.sub-header .nav-item {
    color: var(--text-dark);
}

.sub-header .nav-item:hover {
    background: var(--bg-light);
}

.sub-header .nav-item.active {
    background: var(--primary-pink);
    color: white;
}

/* 响应式适配 */
@media (max-width: 480px) {
    .header {
        padding: 8px 10px 12px;
    }
    
    .nav-container {
        gap: 8px;
    }
    
    .nav-item {
        padding: 6px 8px;
        font-size: 12px;
    }
    
    .logo-icon {
        width: 32px;
        height: 32px;
    }

    .user-icon {
        font-size: 14px;
    }
}

@media (max-width: 360px) {
    .nav-item {
        padding: 4px 6px;
        font-size: 11px;
    }
    
    .user-icon {
        font-size: 12px;
    }
} 