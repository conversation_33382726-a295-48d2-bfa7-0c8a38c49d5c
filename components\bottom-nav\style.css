.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50px;
    background-color: #fff;
    display: flex;
    justify-content: space-around;
    align-items: center;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 1000;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #666;
    font-size: 12px;
    padding: 4px 0;
}

.nav-item.active {
    color: #ff69b4; /* 粉色主题色 */
}

.nav-icon {
    width: 24px;
    height: 24px;
    margin-bottom: 2px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.home-icon {
    background-image: url('../../images/home.png');
}

.order-icon {
    background-image: url('../../images/order.png');
}

.profile-icon {
    background-image: url('../../images/profile.png');
} 