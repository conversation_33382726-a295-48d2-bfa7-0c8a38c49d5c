// 服务功能模块
const Services = {
    // 服务数据
    serviceData: {
        'bazi': {
            name: '八字精批',
            price: '¥29.9',
            description: '深度解析生辰八字，预测人生运势',
            features: ['事业运势', '财富运程', '感情婚姻', '健康状况']
        },
        'marriage': {
            name: '八字合婚',
            price: '¥19.9',
            description: '测算两人八字匹配度，预测婚姻运势',
            features: ['缘分指数', '性格匹配', '运势互补', '婚姻建议']
        },
        'name-detail': {
            name: '姓名详批',
            price: '¥15.9',
            description: '全面分析姓名寓意，解读人生命运',
            features: ['姓名评分', '五行分析', '运势解读', '改名建议']
        },
        'baby-name': {
            name: '宝宝起名',
            price: '¥9.9',
            description: '根据生辰八字起吉祥好名',
            features: ['八字分析', '五行平衡', '寓意解析', '多个备选']
        },
        'career': {
            name: '事业运势',
            price: '¥35.9',
            description: '分析事业发展方向和财运趋势',
            features: ['职业规划', '财运分析', '贵人运势', '发展建议']
        },
        'love': {
            name: '桃花运势',
            price: '¥25.9',
            description: '预测爱情运势和桃花机会',
            features: ['桃花运程', '姻缘分析', '脱单时机', '感情建议']
        }
    },

    // 初始化服务
    init() {
        this.setupServiceCards();
        this.setupCalculationForms();
        this.loadServiceHistory();
    },

    // 设置服务卡片
    setupServiceCards() {
        const serviceCards = document.querySelectorAll('[data-service]');
        
        serviceCards.forEach(card => {
            card.addEventListener('click', (e) => {
                const serviceType = card.dataset.service;
                this.handleServiceClick(serviceType, card);
            });
        });
    },

    // 处理服务点击
    handleServiceClick(serviceType, element) {
        // 添加点击动画
        this.addClickAnimation(element);
        
        // 根据服务类型处理
        const serviceData = this.serviceData[serviceType];
        if (serviceData) {
            this.showServiceModal(serviceType, serviceData);
        } else {
            // 处理特殊服务
            this.handleSpecialService(serviceType);
        }
    },

    // 显示服务弹窗
    showServiceModal(serviceType, serviceData) {
        // 直接跳转到对应页面
        const serviceRoutes = {
            'bazi': 'pages/bazi/index.html',
            'marriage': 'pages/marriage/index.html',
            'name-analysis': 'pages/naming/index.html',
            'name-detail': 'pages/naming/index.html',
            'portrait': 'pages/tarot/index.html',
            'tarot': 'pages/tarot/index.html',
            'name-match': 'pages/naming/index.html',
            'fortune': 'pages/wealth/index.html',
            'baby-name': 'pages/baby-naming/index.html',
            'zodiac': 'pages/zodiac/index.html',
            'phone': 'pages/phone/index.html'
        };
        const route = serviceRoutes[serviceType];
        if (route) {
            window.location.href = route;
        } else {
            alert('该服务暂未开放');
        }
    },

    // 生成表单
    generateForm(serviceType) {
        const baseForm = `
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-size: 14px; color: #333;">姓名</label>
                <input type="text" name="name" placeholder="请输入您的姓名" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-size: 14px; color: #333;">性别</label>
                <div style="display: flex; gap: 20px;">
                    <label style="display: flex; align-items: center; gap: 5px; font-size: 14px;">
                        <input type="radio" name="gender" value="male" checked>
                        <span>男</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: 5px; font-size: 14px;">
                        <input type="radio" name="gender" value="female">
                        <span>女</span>
                    </label>
                </div>
            </div>
        `;

        let specificForm = '';
        
        switch(serviceType) {
            case 'bazi':
            case 'career':
                specificForm = `
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-size: 14px; color: #333;">出生日期</label>
                        <input type="date" name="birthdate" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-size: 14px; color: #333;">出生时间</label>
                        <input type="time" name="birthtime" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                    </div>
                `;
                break;
            case 'marriage':
                specificForm = `
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-size: 14px; color: #333;">您的出生日期</label>
                        <input type="date" name="birthdate1" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-size: 14px; color: #333;">对方姓名</label>
                        <input type="text" name="partnername" placeholder="请输入对方姓名" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-size: 14px; color: #333;">对方出生日期</label>
                        <input type="date" name="birthdate2" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                    </div>
                `;
                break;
        }

        return baseForm + specificForm;
    },

    // 处理计算
    async processCalculation(serviceType, modal) {
        // 获取表单数据
        const formData = new FormData(modal.querySelector('.calculation-form'));
        const data = Object.fromEntries(formData);

        // 验证必填字段
        if (!this.validateForm(data, serviceType)) {
            this.showNotification('提示', '请填写完整信息');
            return;
        }

        // 显示加载状态
        const confirmBtn = modal.querySelector('.confirm-btn');
        const originalText = confirmBtn.textContent;
        confirmBtn.textContent = '创建订单中...';
        confirmBtn.disabled = true;

        try {
            // 准备订单数据
            const orderData = {
                serviceType: serviceType,
                serviceName: this.serviceData[serviceType]?.name || this.getServiceName(serviceType),
                amount: parseFloat(this.serviceData[serviceType]?.price?.replace('¥', '') || '29.9'),
                userData: JSON.stringify(data)
            };

            // 调用后端API创建订单
            const order = await OrderAPI.createOrder(orderData);
            
            // 保存到历史记录
            this.saveToHistory(serviceType, data);

            // 关闭弹窗
            this.closeModal(modal);

            // 显示支付弹窗
            setTimeout(() => {
                this.showPaymentModal(order);
            }, 500);
            
        } catch (error) {
            this.showNotification('错误', '创建订单失败，请重试');
            console.error('创建订单失败:', error);
        } finally {
            // 恢复按钮状态
            confirmBtn.textContent = originalText;
            confirmBtn.disabled = false;
        }
    },

    // 验证表单
    validateForm(data, serviceType) {
        if (!data.name) return false;
        
        switch(serviceType) {
            case 'bazi':
            case 'career':
                return data.birthdate && data.birthtime;
            case 'marriage':
                return data.birthdate1 && data.partnername && data.birthdate2;
            default:
                return true;
        }
    },

    // 显示计算结果
    showCalculationResult(serviceType, data) {
        const result = this.generateResult(serviceType, data);
        const modal = this.createResultModal(serviceType, result);
        document.body.appendChild(modal);
        
        requestAnimationFrame(() => {
            modal.style.opacity = '1';
        });
    },

    // 生成结果
    generateResult(serviceType, data) {
        // 这里可以集成真实的算命算法，目前使用模拟数据
        const results = {
            'bazi': {
                score: Math.floor(Math.random() * 20) + 80,
                summary: '您的八字显示天资聪颖，事业运势较佳',
                details: [
                    '事业：适合从事创意性工作，贵人运强',
                    '财运：财来财去，需注意理财规划',
                    '感情：桃花运旺盛，但需谨慎选择',
                    '健康：整体良好，注意作息规律'
                ]
            },
            'marriage': {
                score: Math.floor(Math.random() * 30) + 70,
                summary: '两人八字匹配度较高，婚姻运势不错',
                details: [
                    '性格匹配：互补性强，能够相互包容',
                    '事业互助：双方事业能够相互促进',
                    '财运组合：财运互补，家庭财政稳定',
                    '子女运势：子女聪明健康，家庭和睦'
                ]
            }
        };

        return results[serviceType] || {
            score: 85,
            summary: '运势整体良好，未来发展可期',
            details: ['运势稳中有升', '贵人相助明显', '需把握机遇', '保持积极心态']
        };
    },

    // 创建结果弹窗
    createResultModal(serviceType, result) {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            z-index: 1001;
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        `;

        modal.innerHTML = `
            <div style="background: white; border-radius: 15px; padding: 25px; max-width: 400px; width: 100%; text-align: center; position: relative;">
                <div style="position: absolute; top: 15px; right: 15px; cursor: pointer; font-size: 20px; color: #999;" onclick="this.parentNode.parentNode.remove()">&times;</div>
                
                <div style="margin-bottom: 20px;">
                    <div style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, #FF69B4, #FF1493); margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold;">
                        ${result.score}
                    </div>
                    <h3 style="color: #FF69B4; margin-bottom: 10px;">测算完成</h3>
                    <p style="color: #666; font-size: 14px; line-height: 1.5;">${result.summary}</p>
                </div>
                
                <div style="text-align: left; margin-bottom: 20px;">
                    <h4 style="color: #333; margin-bottom: 10px; font-size: 16px;">详细解析</h4>
                    ${result.details.map(detail => `
                        <div style="margin-bottom: 8px; padding: 8px; background: #F8F9FA; border-radius: 8px; font-size: 13px; color: #666;">
                            ${detail}
                        </div>
                    `).join('')}
                </div>
                
                <div style="display: flex; gap: 10px;">
                    <button style="flex: 1; background: #f0f0f0; color: #666; border: none; border-radius: 20px; padding: 10px; cursor: pointer;" onclick="this.parentNode.parentNode.parentNode.remove()">关闭</button>
                    <button style="flex: 1; background: linear-gradient(135deg, #FF69B4, #FF1493); color: white; border: none; border-radius: 20px; padding: 10px; cursor: pointer;" onclick="this.parentNode.parentNode.parentNode.remove()">分享结果</button>
                </div>
            </div>
        `;

        return modal;
    },

    // 处理特殊服务
    handleSpecialService(serviceType) {
        switch(serviceType) {
            case 'portrait':
                console.log('💕 姻缘画像按钮被点击了！跳转到独立页面...');
                window.location.href = 'pages/tarot/index.html';
                break;
            case 'tarot':
                this.showTarotReading();
                break;
            default:
                App.showComingSoon('该功能');
        }
    },

    // 塔罗占卜
    showTarotReading() {
        const cards = [
            { name: '愚者', meaning: '新的开始，充满可能性' },
            { name: '魔术师', meaning: '创造力强，能实现目标' },
            { name: '女祭司', meaning: '直觉敏锐，内在智慧' },
            { name: '皇后', meaning: '丰富多产，母性关怀' },
            { name: '皇帝', meaning: '权威稳定，理性思考' },
            { name: '教皇', meaning: '传统智慧，精神指导' },
            { name: '恋人', meaning: '和谐关系，重要选择' },
            { name: '战车', meaning: '意志坚定，克服困难' },
            { name: '力量', meaning: '内在力量，温柔制胜' },
            { name: '隐者', meaning: '内省思考，寻求真理' }
        ];

        const randomCard = cards[Math.floor(Math.random() * cards.length)];
        
        App.showNotification(`您抽到了：${randomCard.name}`, randomCard.meaning);
    },

    // 添加点击动画
    addClickAnimation(element) {
        element.style.transform = 'scale(0.95)';
        setTimeout(() => {
            element.style.transform = '';
        }, 150);
    },

    // 关闭弹窗
    closeModal(modal) {
        modal.style.opacity = '0';
        if (modal.querySelector('.modal-content')) {
            modal.querySelector('.modal-content').style.transform = 'translateY(100%)';
        }
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 300);
    },

    // 保存历史记录
    saveToHistory(serviceType, data) {
        const history = JSON.parse(localStorage.getItem('calculationHistory') || '[]');
        history.unshift({
            type: serviceType,
            data: data,
            timestamp: Date.now()
        });
        
        // 只保留最近10条记录
        if (history.length > 10) {
            history.splice(10);
        }
        
        localStorage.setItem('calculationHistory', JSON.stringify(history));
    },

    // 加载历史记录
    loadServiceHistory() {
        const history = JSON.parse(localStorage.getItem('calculationHistory') || '[]');
        // 这里可以在界面上显示历史记录
        console.log('计算历史:', history);
    },

    // 显示支付弹窗
    showPaymentModal(order) {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            z-index: 1000;
            opacity: 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        modal.innerHTML = `
            <div style="background: white; border-radius: 15px; padding: 25px; width: 90%; max-width: 400px; max-height: 80vh; overflow-y: auto; transform: translateY(50px); transition: transform 0.3s ease;">
                <div style="text-align: center; margin-bottom: 20px;">
                    <h2 style="color: #FF69B4; margin-bottom: 10px; font-size: 20px;">确认支付</h2>
                    <div style="background: linear-gradient(135deg, #FF69B4, #FF1493); color: white; padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <div style="font-size: 12px; opacity: 0.9;">支付金额</div>
                        <div style="font-size: 28px; font-weight: 700;">¥${order.amount}</div>
                    </div>
                    <div style="font-size: 14px; color: #666;">
                        <div>订单号：${order.id}</div>
                        <div>服务：${order.serviceName}</div>
                    </div>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h3 style="font-size: 16px; margin-bottom: 15px; color: #333;">选择支付方式</h3>
                    <div>
                        <div class="payment-method" data-method="wechat" style="display: flex; align-items: center; padding: 15px; border: 2px solid #f0f0f0; border-radius: 10px; margin-bottom: 10px; cursor: pointer; transition: all 0.2s ease;">
                            <div style="font-size: 24px; margin-right: 15px;">💚</div>
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: #333;">微信支付</div>
                                <div style="font-size: 12px; color: #666; margin-top: 2px;">使用微信扫码支付</div>
                            </div>
                            <div class="check-icon" style="width: 20px; height: 20px; border: 2px solid #ddd; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;"></div>
                        </div>
                        <div class="payment-method" data-method="alipay" style="display: flex; align-items: center; padding: 15px; border: 2px solid #f0f0f0; border-radius: 10px; margin-bottom: 10px; cursor: pointer; transition: all 0.2s ease;">
                            <div style="font-size: 24px; margin-right: 15px;">💙</div>
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: #333;">支付宝</div>
                                <div style="font-size: 12px; color: #666; margin-top: 2px;">使用支付宝扫码支付</div>
                            </div>
                            <div class="check-icon" style="width: 20px; height: 20px; border: 2px solid #ddd; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;"></div>
                        </div>
                    </div>
                </div>
                
                <div style="display: flex; gap: 10px;">
                    <button class="cancel-payment" style="flex: 1; background: #f0f0f0; color: #666; border: none; border-radius: 20px; padding: 12px; font-weight: 600; cursor: pointer;">取消</button>
                    <button class="confirm-payment" style="flex: 2; background: linear-gradient(135deg, #FF69B4, #FF1493); color: white; border: none; border-radius: 20px; padding: 12px; font-weight: 600; cursor: pointer; opacity: 0.5;" disabled>立即支付</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        
        // 显示动画
        requestAnimationFrame(() => {
            modal.style.opacity = '1';
            modal.querySelector('div').style.transform = 'translateY(0)';
        });

        // 绑定事件
        let selectedMethod = null;
        
        // 选择支付方式
        modal.querySelectorAll('.payment-method').forEach(el => {
            el.addEventListener('click', function() {
                modal.querySelectorAll('.payment-method').forEach(m => {
                    m.style.borderColor = '#f0f0f0';
                    m.querySelector('.check-icon').style.background = 'transparent';
                    m.querySelector('.check-icon').innerHTML = '';
                });
                
                this.style.borderColor = '#FF69B4';
                this.querySelector('.check-icon').style.background = '#FF69B4';
                this.querySelector('.check-icon').innerHTML = '✓';
                
                selectedMethod = this.dataset.method;
                modal.querySelector('.confirm-payment').style.opacity = '1';
                modal.querySelector('.confirm-payment').disabled = false;
            });
        });

        // 取消支付
        modal.querySelector('.cancel-payment').addEventListener('click', () => {
            this.closeModal(modal);
        });

        // 确认支付
        modal.querySelector('.confirm-payment').addEventListener('click', async () => {
            if (!selectedMethod) {
                this.showNotification('提示', '请选择支付方式');
                return;
            }

            const confirmBtn = modal.querySelector('.confirm-payment');
            const originalText = confirmBtn.textContent;
            confirmBtn.textContent = '支付中...';
            confirmBtn.disabled = true;

            try {
                await OrderAPI.payOrder(order.id);
                this.showPaymentSuccess(modal, order);
            } catch (error) {
                this.showPaymentFailed(modal, error.message);
                confirmBtn.textContent = originalText;
                confirmBtn.disabled = false;
            }
        });

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal(modal);
            }
        });
    },

    // 显示支付成功
    showPaymentSuccess(modal, order) {
        const content = modal.querySelector('div');
        content.innerHTML = `
            <div style="text-align: center; padding: 30px 20px;">
                <div style="font-size: 60px; margin-bottom: 20px;">✅</div>
                <h2 style="color: #52c41a; margin-bottom: 15px;">支付成功</h2>
                <p style="color: #666; margin-bottom: 20px;">订单 ${order.id} 支付成功！</p>
                <div style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 10px; padding: 15px; margin-bottom: 20px;">
                    <div style="font-size: 14px; color: #52c41a;">
                        <div>服务：${order.serviceName}</div>
                        <div>金额：¥${order.amount}</div>
                        <div>时间：${new Date().toLocaleString()}</div>
                    </div>
                </div>
                <button class="close-success" style="background: linear-gradient(135deg, #52c41a, #389e0d); color: white; border: none; border-radius: 20px; padding: 12px 30px; font-weight: 600; cursor: pointer;">确定</button>
            </div>
        `;

        content.querySelector('.close-success').addEventListener('click', () => {
            this.closeModal(modal);
            setTimeout(() => {
                window.location.href = 'pages/order/index.html';
            }, 500);
        });
    },

    // 显示支付失败
    showPaymentFailed(modal, errorMessage) {
        const content = modal.querySelector('div');
        content.innerHTML = `
            <div style="text-align: center; padding: 30px 20px;">
                <div style="font-size: 60px; margin-bottom: 20px;">❌</div>
                <h2 style="color: #ff4d4f; margin-bottom: 15px;">支付失败</h2>
                <p style="color: #666; margin-bottom: 20px;">${errorMessage}</p>
                <div style="background: #fff2f0; border: 1px solid #ffccc7; border-radius: 10px; padding: 15px; margin-bottom: 20px;">
                    <div style="font-size: 14px; color: #ff4d4f;">请检查网络连接或稍后重试</div>
                </div>
                <div style="display: flex; gap: 10px;">
                    <button class="retry-payment" style="flex: 1; background: linear-gradient(135deg, #ff4d4f, #cf1322); color: white; border: none; border-radius: 20px; padding: 12px; font-weight: 600; cursor: pointer;">重试</button>
                    <button class="close-failed" style="flex: 1; background: #f0f0f0; color: #666; border: none; border-radius: 20px; padding: 12px; font-weight: 600; cursor: pointer;">取消</button>
                </div>
            </div>
        `;

        content.querySelector('.retry-payment').addEventListener('click', () => {
            this.showPaymentModal(order);
        });

        content.querySelector('.close-failed').addEventListener('click', () => {
            this.closeModal(modal);
        });
    },

    // 获取服务名称
    getServiceName(serviceType) {
        const serviceNames = {
            'bazi': '八字精批',
            'marriage': '八字合婚',
            'name-detail': '姓名详批',
            'baby-name': '宝宝起名',
            'career': '事业运势',
            'love': '桃花运势',
            'fortune': '财运预测',
            'zodiac': '生肖运势',
            'phone': '手机号测吉凶',
            'portrait': '姻缘画像',
            'name-analysis': '姓名详批'
        };
        return serviceNames[serviceType] || '未知服务';
    },

    // 显示通知
    showNotification(title, message) {
        if (window.App && window.App.showNotification) {
            window.App.showNotification(title, message);
        }
    }
};

// 导出到全局
window.Services = Services; 