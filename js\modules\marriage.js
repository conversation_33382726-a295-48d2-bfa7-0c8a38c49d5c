// 八字合婚模块
class MarriageModule {
    constructor() {
        this.modal = null;
        this.canvas = null;
        this.ctx = null;
        this.animationId = null;
        this.hearts = [];
        this.redThread = null;
        this.init();
    }

    init() {
        this.modal = document.getElementById('marriageModal');
        this.canvas = document.getElementById('marriageCanvas');
        
        if (this.canvas) {
            this.ctx = this.canvas.getContext('2d');
            this.initCanvas();
            this.createHearts();
            this.initRedThread();
            this.animate();
        }

        this.initForm();
        this.bindEvents();
    }

    // 初始化Canvas
    initCanvas() {
        const resizeCanvas = () => {
            const rect = this.canvas.parentElement.getBoundingClientRect();
            this.canvas.width = rect.width;
            this.canvas.height = rect.height;
        };
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
    }

    // 创建爱心粒子
    createHearts() {
        this.hearts = [];
        const heartCount = 20;
        
        for (let i = 0; i < heartCount; i++) {
            this.hearts.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                size: Math.random() * 15 + 10,
                speedX: (Math.random() - 0.5) * 0.3,
                speedY: (Math.random() - 0.5) * 0.3,
                opacity: Math.random() * 0.6 + 0.2,
                color: this.getRandomHeartColor(),
                pulse: Math.random() * Math.PI * 2
            });
        }
    }

    // 获取随机爱心颜色
    getRandomHeartColor() {
        const colors = [
            'rgba(255, 105, 180, ',    // 热粉色
            'rgba(255, 182, 193, ',    // 浅粉色
            'rgba(255, 20, 147, ',     // 深粉色
            'rgba(255, 192, 203, ',    // 粉红色
            'rgba(255, 69, 0, '        // 红橙色
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    // 初始化红线
    initRedThread() {
        this.redThread = {
            points: [],
            wave: 0
        };
        
        // 创建波浪形红线点
        const pointCount = 50;
        for (let i = 0; i < pointCount; i++) {
            this.redThread.points.push({
                x: (this.canvas.width / pointCount) * i,
                y: this.canvas.height / 2,
                originalY: this.canvas.height / 2
            });
        }
    }

    // 绘制爱心
    drawHeart(x, y, size, color, opacity) {
        this.ctx.save();
        this.ctx.translate(x, y);
        this.ctx.scale(size / 20, size / 20);
        
        this.ctx.fillStyle = color + opacity + ')';
        this.ctx.beginPath();
        this.ctx.moveTo(0, 3);
        this.ctx.bezierCurveTo(-9, -6, -18, 0, 0, 15);
        this.ctx.bezierCurveTo(18, 0, 9, -6, 0, 3);
        this.ctx.fill();
        
        this.ctx.restore();
    }

    // 绘制双心（大爱心）
    drawDoubleHeart(x, y, size, rotation) {
        this.ctx.save();
        this.ctx.translate(x, y);
        this.ctx.rotate(rotation);
        
        // 第一个心（粉色）
        this.drawHeart(-size * 0.3, 0, size * 0.8, 'rgba(255, 105, 180, ', 0.3);
        
        // 第二个心（红色）
        this.drawHeart(size * 0.3, 0, size * 0.8, 'rgba(255, 20, 147, ', 0.3);
        
        this.ctx.restore();
    }

    // 绘制红线（月老红线）
    drawRedThread() {
        if (this.redThread.points.length === 0) return;
        
        this.ctx.strokeStyle = 'rgba(220, 20, 60, 0.4)';
        this.ctx.lineWidth = 3;
        this.ctx.lineCap = 'round';
        
        this.ctx.beginPath();
        this.ctx.moveTo(this.redThread.points[0].x, this.redThread.points[0].y);
        
        for (let i = 1; i < this.redThread.points.length; i++) {
            const point = this.redThread.points[i];
            this.ctx.lineTo(point.x, point.y);
        }
        
        this.ctx.stroke();
        
        // 绘制红线端点的小心形
        this.drawHeart(this.redThread.points[0].x, this.redThread.points[0].y, 8, 'rgba(220, 20, 60, ', 0.8);
        const lastPoint = this.redThread.points[this.redThread.points.length - 1];
        this.drawHeart(lastPoint.x, lastPoint.y, 8, 'rgba(220, 20, 60, ', 0.8);
    }

    // 绘制汉字"囍"
    drawXiCharacter(x, y, size) {
        this.ctx.save();
        this.ctx.fillStyle = 'rgba(255, 215, 0, 0.2)';
        this.ctx.font = `${size}px serif`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('囍', x, y);
        this.ctx.restore();
    }

    // 动画循环
    animate() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        const time = Date.now() * 0.001;

        // 更新和绘制爱心粒子
        this.hearts.forEach(heart => {
            heart.x += heart.speedX;
            heart.y += heart.speedY;
            heart.pulse += 0.02;

            // 边界检查
            if (heart.x < -heart.size) heart.x = this.canvas.width + heart.size;
            if (heart.x > this.canvas.width + heart.size) heart.x = -heart.size;
            if (heart.y < -heart.size) heart.y = this.canvas.height + heart.size;
            if (heart.y > this.canvas.height + heart.size) heart.y = -heart.size;

            // 脉动效果
            const pulseFactor = 1 + Math.sin(heart.pulse) * 0.2;
            const currentOpacity = heart.opacity + Math.sin(heart.pulse) * 0.2;

            this.drawHeart(heart.x, heart.y, heart.size * pulseFactor, heart.color, currentOpacity);
        });

        // 更新红线波浪
        this.redThread.wave += 0.02;
        this.redThread.points.forEach((point, index) => {
            point.y = point.originalY + Math.sin(this.redThread.wave + index * 0.1) * 20;
        });

        // 绘制红线
        this.drawRedThread();

        // 绘制大双心
        this.drawDoubleHeart(
            this.canvas.width * 0.2, 
            this.canvas.height * 0.2, 
            40, 
            time * 0.3
        );

        this.drawDoubleHeart(
            this.canvas.width * 0.8, 
            this.canvas.height * 0.8, 
            35, 
            -time * 0.4
        );

        // 绘制囍字
        this.drawXiCharacter(
            this.canvas.width * 0.5, 
            this.canvas.height * 0.15, 
            60
        );

        this.animationId = requestAnimationFrame(() => this.animate());
    }

    // 初始化表单
    initForm() {
        this.bindFormEvents();
    }

    // 绑定表单事件
    bindFormEvents() {
        // 表单提交事件
        const form = document.getElementById('marriageForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmit();
            });
        }
    }

    // 处理表单提交
    handleFormSubmit() {
        const formData = new FormData(document.getElementById('marriageForm'));
        
        // 从date字段获取数据
        const maleDateValue = formData.get('maleBirthDate');
        const femaleDateValue = formData.get('femaleBirthDate');
        
        // 解析日期
        const maleDate = maleDateValue ? new Date(maleDateValue) : null;
        const femaleDate = femaleDateValue ? new Date(femaleDateValue) : null;
        
        const data = {
            male: {
                name: formData.get('maleName'),
                date: maleDateValue,
                year: maleDate ? maleDate.getFullYear() : null,
                month: maleDate ? maleDate.getMonth() + 1 : null,
                day: maleDate ? maleDate.getDate() : null,
                hour: formData.get('maleBirthHour')
            },
            female: {
                name: formData.get('femaleName'),
                date: femaleDateValue,
                year: femaleDate ? femaleDate.getFullYear() : null,
                month: femaleDate ? femaleDate.getMonth() + 1 : null,
                day: femaleDate ? femaleDate.getDate() : null,
                hour: formData.get('femaleBirthHour')
            }
        };

        // 验证数据
        const maleComplete = data.male.name && data.male.date && data.male.hour;
        const femaleComplete = data.female.name && data.female.date && data.female.hour;

        if (!maleComplete || !femaleComplete) {
            alert('请填写完整的双方姓名、生日和时辰信息');
            return;
        }

        console.log('八字合婚数据:', data);
        
        // 计算匹配度（模拟）
        const compatibility = Math.floor(Math.random() * 30) + 70; // 70-99%
        
        // 获取时辰名称
        const getHourName = (hourValue) => {
            const hourMap = {
                'zi': '子时',
                'chou': '丑时',
                'yin': '寅时',
                'mao': '卯时',
                'chen': '辰时',
                'si': '巳时',
                'wu': '午时',
                'wei': '未时',
                'shen': '申时',
                'you': '酉时',
                'xu': '戌时',
                'hai': '亥时'
            };
            return hourMap[hourValue] || hourValue;
        };
        
        // 格式化显示时间
        const maleTimeStr = `${data.male.year}年${data.male.month}月${data.male.day}日 ${getHourName(data.male.hour)}`;
        const femaleTimeStr = `${data.female.year}年${data.female.month}月${data.female.day}日 ${getHourName(data.female.hour)}`;
        
        alert(`💕 ${data.male.name} & ${data.female.name} 的合婚结果：\n\n姻缘匹配度：${compatibility}%\n\n男方生辰：${maleTimeStr}\n女方生辰：${femaleTimeStr}\n\n您们的八字信息已提交给专业大师，详细的合婚分析报告将在5-10分钟内完成。\n\n愿有情人终成眷属！`);
        
        this.closeModal();
    }

    // 绑定事件
    bindEvents() {
        // 绑定服务项点击事件
        document.addEventListener('click', (e) => {
            const serviceItem = e.target.closest('[data-service="marriage"]');
            if (serviceItem) {
                console.log('💕 八字合婚按钮被点击了！跳转到独立页面...');
                // 跳转到独立页面而不是打开模态框
                window.location.href = 'pages/marriage/index.html';
            }
        });

        // 点击遮罩关闭模态框
        if (this.modal) {
            this.modal.addEventListener('click', (e) => {
                if (e.target === this.modal) {
                    this.closeModal();
                }
            });
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('show')) {
                this.closeModal();
            }
        });
    }

    // 打开模态框
    openModal() {
        if (this.modal) {
            this.modal.classList.add('show');
            this.modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            
            // 重新初始化Canvas
            setTimeout(() => {
                this.initCanvas();
                this.createHearts();
                this.initRedThread();
            }, 100);
        }
    }

    // 关闭模态框
    closeModal() {
        if (this.modal) {
            this.modal.classList.remove('show');
            setTimeout(() => {
                this.modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }, 300);
        }
    }

    // 销毁动画
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
    }
}

// 全局函数供HTML调用
function closeMarriageModal() {
    if (window.marriageModule) {
        window.marriageModule.closeModal();
    }
}

// 初始化合婚模块
document.addEventListener('DOMContentLoaded', () => {
    window.marriageModule = new MarriageModule();
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (window.marriageModule) {
        window.marriageModule.destroy();
    }
}); 