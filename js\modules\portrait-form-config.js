/**
 * 姻缘画像表单配置
 * 简化的参数配置类
 */
class PortraitFormConfig {
    constructor() {
        this.config = {
            appearance: {
                age: { type: 'range', label: '理想年龄', min: 18, max: 50, default: 25 },
                height: { type: 'range', label: '理想身高', min: 150, max: 190, default: 165 },
                bodyType: { type: 'select', label: '体型', options: ['苗条', '运动型', '标准', '丰满'], default: '标准' },
                style: { type: 'select', label: '风格', options: ['优雅', '休闲', '运动', '艺术'], default: '优雅' }
            },
            personality: {
                primary: { type: 'multi', label: '性格特征', options: ['温柔', '活泼', '知性', '成熟', '浪漫'], default: ['温柔'] },
                communication: { type: 'select', label: '沟通方式', options: ['直接', '温和', '幽默'], default: '温和' }
            },
            lifestyle: {
                activities: { type: 'multi', label: '兴趣爱好', options: ['健身', '旅行', '读书', '音乐', '烹饪'], default: ['健身'] },
                livingStyle: { type: 'select', label: '生活风格', options: ['有条理', '随性', '极简'], default: '有条理' }
            },
            career: {
                field: { type: 'multi', label: '行业领域', options: ['教育', '医疗', '科技', '艺术', '商务'], default: ['教育'] },
                level: { type: 'select', label: '职业阶段', options: ['入门', '中层', '高级', '专家'], default: '中层' }
            },
            values: {
                family: { type: 'select', label: '家庭观念', options: ['传统', '现代', '灵活'], default: '传统' },
                finance: { type: 'select', label: '理财观念', options: ['保守', '稳健', '积极'], default: '保守' }
            },
            relationship: {
                commitment: { type: 'select', label: '承诺程度', options: ['随缘', '认真', '以结婚为目的'], default: '认真' },
                timeline: { type: 'select', label: '时间规划', options: ['立即', '6个月内', '1-2年', '随缘'], default: '1-2年' }
            }
        };
    }

    getConfig() {
        return this.config;
    }

    getDefaults() {
        const defaults = {};
        for (const category in this.config) {
            defaults[category] = {};
            for (const field in this.config[category]) {
                const fieldConfig = this.config[category][field];
                if (fieldConfig.default !== undefined) {
                    defaults[category][field] = fieldConfig.default;
                }
            }
        }
        return defaults;
    }

    // 生成表单HTML
    generateFormHTML(category = null) {
        let html = '<div class="portrait-form">';
        
        if (category) {
            // 只生成指定分类的表单
            if (this.config[category]) {
                html += `<div class="form-category">
                    <h3 class="category-title">${this.getCategoryTitle(category)}</h3>
                    <div class="category-fields">`;
                
                for (const field in this.config[category]) {
                    const fieldConfig = this.config[category][field];
                    html += this.generateFieldHTML(category, field, fieldConfig);
                }
                
                html += '</div></div>';
            }
        } else {
            // 生成所有分类的表单
            for (const category in this.config) {
                html += `<div class="form-category">
                    <h3 class="category-title">${this.getCategoryTitle(category)}</h3>
                    <div class="category-fields">`;
                
                for (const field in this.config[category]) {
                    const fieldConfig = this.config[category][field];
                    html += this.generateFieldHTML(category, field, fieldConfig);
                }
                
                html += '</div></div>';
            }
        }
        
        html += '</div>';
        return html;
    }

    getCategoryTitle(category) {
        const titles = {
            appearance: '外貌特征',
            personality: '性格特征',
            lifestyle: '生活方式',
            career: '职业发展',
            values: '价值观',
            relationship: '关系期望'
        };
        return titles[category] || category;
    }

    generateFieldHTML(category, field, config) {
        const fieldId = `${category}_${field}`;
        const fieldName = `${category}[${field}]`;
        
        let html = `<div class="form-field">
            <label for="${fieldId}" class="field-label">${config.label}</label>`;
        
        if (config.type === 'range') {
            html += `<input type="range" id="${fieldId}" name="${fieldName}" 
                min="${config.min}" max="${config.max}" value="${config.default}" class="range-input">
            <span class="range-value">${config.default}</span>`;
        } else if (config.type === 'select') {
            html += `<select id="${fieldId}" name="${fieldName}" class="select-input">`;
            for (const option of config.options) {
                const selected = option === config.default ? 'selected' : '';
                html += `<option value="${option}" ${selected}>${option}</option>`;
            }
            html += '</select>';
        } else if (config.type === 'multi') {
            html += `<div class="multi-select-container">`;
            for (const option of config.options) {
                const checked = config.default.includes(option) ? 'checked' : '';
                html += `<label class="checkbox-label">
                    <input type="checkbox" name="${fieldName}" value="${option}" ${checked}>
                    <span class="checkbox-text">${option}</span>
                </label>`;
            }
            html += '</div>';
        }
        
        html += '</div>';
        return html;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { PortraitFormConfig };
} 