<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>💕 八字合婚 - 易海堂算命网</title>
    <link rel="stylesheet" href="../../css/main.css">
    <!-- 认证服务 -->
    <script src="../../js/modules/unified-auth-service.js"></script>
    <script src="../../js/modules/member-service.js"></script>
    <script src="../../js/modules/auth-service.js"></script>
    <!-- 认证初始化 -->
    <script src="../../js/modules/auth-init.js"></script>
    <!-- AI服务 -->
    <script src="../../js/modules/ai-service.js"></script>
    <!-- 订单和支付服务 -->
    <script src="../../js/modules/api-order.js"></script>
    <script src="../../js/modules/order-payment.js"></script>
    <!-- 业务模块 -->
    <script src="../../js/modules/marriage-ai.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #FFB6C1, #FF69B4, #DC143C);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            color: #8B0000;
        }
        
        .marriage-container {
            position: relative;
            min-height: 100vh;
            overflow: hidden;
        }
        
        .marriage-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .marriage-content {
            position: relative;
            z-index: 2;
            padding: 0;
        }
        
        .marriage-header {
            background: linear-gradient(135deg, #DC143C, #FF1493);
            color: white;
            text-align: center;
            padding: 20px;
            position: relative;
            border-bottom: 2px solid #FFB6C1;
        }
        
        .marriage-header h1 {
            margin: 0;
            font-size: 24px;
            position: relative;
            z-index: 1;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }
        
        .back-btn {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid white;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            z-index: 2;
        }
        
        .form-container {
            padding: 25px 20px;
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            margin-top: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 182, 193, 0.3);
        }
        
        .form-container h3 {
            color: #DC143C;
            text-align: center;
            margin-bottom: 8px;
            text-shadow: 0 0 10px rgba(220, 20, 60, 0.5);
        }
        
        .form-container p {
            color: #8B0000;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .couple-forms {
            display: flex;
            gap: 20px;
        }
        
        .person-form {
            flex: 1;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            border: 2px solid rgba(255, 20, 147, 0.3);
        }
        
        .person-form.male {
            border-color: rgba(70, 130, 180, 0.5);
        }
        
        .person-form.female {
            border-color: rgba(255, 20, 147, 0.5);
        }
        
        .person-form h4 {
            text-align: center;
            margin-bottom: 15px;
            color: #DC143C;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #8B0000;
            font-weight: 600;
            font-size: 14px;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid rgba(255, 20, 147, 0.3);
            border-radius: 8px;
            background: white;
            color: #2C1810;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #FF1493;
            box-shadow: 0 0 10px rgba(255, 20, 147, 0.3);
        }
        
        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #DC143C, #FF1493, #FFB6C1);
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            color: white;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(220, 20, 60, 0.4);
            margin-top: 20px;
        }
        
        .submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(220, 20, 60, 0.6);
        }
        
        .love-divider {
            text-align: center;
            margin: 15px 0;
            position: relative;
        }
        
        .love-divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #FF1493, transparent);
        }
        
        .love-divider .heart {
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 15px;
            border-radius: 20px;
            color: #DC143C;
            font-size: 18px;
            display: inline-block;
            position: relative;
            z-index: 1;
            animation: heartBeat 2s infinite;
        }
        
        @keyframes heartBeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        /* 婚姻日期选择器样式 */
        .marriage-date-picker {
            cursor: pointer;
            border: 2px solid rgba(255, 20, 147, 0.3);
            border-radius: 8px;
            background: white;
            transition: all 0.3s ease;
            min-height: 40px;
        }

        .marriage-date-picker:hover {
            border-color: #FF1493;
            box-shadow: 0 0 10px rgba(255, 20, 147, 0.3);
        }

        .male-date-picker {
            border-color: rgba(70, 130, 180, 0.5);
        }

        .male-date-picker:hover {
            border-color: #4682B4;
            box-shadow: 0 0 10px rgba(70, 130, 180, 0.3);
        }

        .date-input-display {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 12px;
        }

        .date-placeholder {
            color: #999;
            font-size: 14px;
        }

        .date-placeholder.selected {
            color: #2C1810;
            font-weight: 500;
        }

        .date-picker-btn {
            width: 28px;
            height: 20px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
        }

        .date-picker-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 3px 6px rgba(255, 215, 0, 0.4);
        }

        .picker-icon {
            font-size: 12px;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* 婚姻日期选择器模态框样式 */
        .marriage-date-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 10001;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .marriage-modal-container {
            background: white;
            border-radius: 15px;
            width: 100%;
            max-width: 380px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideUp 0.3s ease-out;
        }

        @keyframes modalSlideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 爱情主题头部 */
        .marriage-modal-header {
            background: linear-gradient(135deg, #FF1493, #FFB6C1);
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .marriage-modal-header.male {
            background: linear-gradient(135deg, #4682B4, #87CEEB);
        }

        .header-icon {
            font-size: 24px;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .marriage-modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* 日期输入框 */
        .date-input-container {
            padding: 20px;
            background: white;
        }

        .date-input-box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 12px 15px;
        }

        .date-value {
            color: #666;
            font-size: 16px;
            font-weight: 500;
        }

        .date-value.selected {
            color: #333;
        }

        .date-confirm-btn {
            width: 36px;
            height: 28px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
        }

        .date-confirm-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 3px 8px rgba(255, 215, 0, 0.4);
        }

        .confirm-icon {
            color: white;
            font-size: 16px;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* 月份导航 */
        .month-navigation {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: white;
            border-bottom: 1px solid #f0f0f0;
        }

        .nav-btn {
            width: 32px;
            height: 32px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            color: #666;
        }

        .nav-btn:hover {
            background: #f8f9fa;
            border-color: #bbb;
            color: #333;
        }

        .current-month {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 600;
            color: #333;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .current-month:hover {
            background: #f8f9fa;
        }

        .month-dropdown {
            font-size: 12px;
            color: #999;
            transition: transform 0.3s ease;
        }

        .current-month:hover .month-dropdown {
            transform: rotate(180deg);
        }

        /* 日历容器 */
        .calendar-container {
            padding: 0 20px 20px;
            background: white;
        }

        /* 星期标题 */
        .week-header {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 0;
            margin-bottom: 10px;
        }

        .week-day {
            padding: 8px 0;
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            color: #666;
        }

        /* 日历网格 */
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 2px;
        }

        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.3s ease;
            color: #333;
            background: white;
            border: 1px solid transparent;
        }

        .calendar-day:hover {
            background: #f8f9fa;
            border-color: #e0e0e0;
        }

        .calendar-day.other-month {
            color: #ccc;
        }

        .calendar-day.selected {
            background: #FF1493;
            color: white;
            font-weight: 600;
        }

        .calendar-day.today {
            border-color: #FFB6C1;
            background: rgba(255, 182, 193, 0.1);
            color: #FF69B4;
            font-weight: 600;
        }

        /* 底部按钮 */
        .calendar-footer {
            display: flex;
            justify-content: space-between;
            padding: 15px 20px;
            background: white;
            border-top: 1px solid #f0f0f0;
        }

        .footer-btn {
            padding: 8px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .clear-btn {
            background: transparent;
            color: #007AFF;
        }

        .clear-btn:hover {
            background: rgba(0, 122, 255, 0.1);
        }

        .today-btn {
            background: transparent;
            color: #007AFF;
        }

        .today-btn:hover {
            background: rgba(0, 122, 255, 0.1);
        }

        /* 收起箭头 */
        .collapse-arrow {
            display: flex;
            justify-content: center;
            padding: 10px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .collapse-arrow:hover {
            background: #f8f9fa;
        }

        .collapse-arrow span {
            color: #999;
            font-size: 16px;
        }

        /* 移动端适配 */
        @media (max-width: 580px) {
            .couple-forms {
                flex-direction: column;
                gap: 15px;
            }
            
            .form-container {
                padding: 15px 10px;
            }
            
            .person-form {
                padding: 15px;
            }
        }

        /* 年份选择器样式 */
        .year-selector {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: white;
            z-index: 10;
            border-radius: 15px;
            overflow: hidden;
        }

        .year-selector-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: linear-gradient(135deg, #DC143C, #FF1493);
            border-bottom: 1px solid #f0f0f0;
        }

        .year-nav-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            color: white;
            font-weight: bold;
        }

        .year-nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        #yearRangeDisplay {
            font-size: 16px;
            font-weight: 600;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .year-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 8px;
            padding: 20px;
            max-height: 280px;
            overflow-y: auto;
        }

        .year-item {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            color: #333;
            background: #f8f9fa;
            border: 1px solid transparent;
        }

        .year-item:hover {
            background: #FFB6C1;
            color: white;
            transform: scale(1.05);
        }

        .year-item.selected {
            background: #DC143C;
            color: white;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(220, 20, 60, 0.3);
        }

        .year-item.current {
            border-color: #FF1493;
            background: rgba(255, 182, 193, 0.1);
            color: #DC143C;
            font-weight: 600;
        }

        .year-selector-footer {
            display: flex;
            justify-content: center;
            padding: 15px 20px;
            background: white;
            border-top: 1px solid #f0f0f0;
        }

        .year-btn {
            padding: 8px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #FF1493;
            color: white;
        }

        .year-btn:hover {
            background: #DC143C;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="marriage-container">
        <!-- Canvas动画背景 -->
        <canvas id="marriageCanvas" class="marriage-canvas"></canvas>
        
        <div class="marriage-content">
            <!-- 页面头部 -->
            <header class="marriage-header">
                <button class="back-btn" onclick="history.back()">← 返回</button>
                <h1>💕 八字合婚</h1>
            </header>
            
            <!-- 表单内容 -->
            <div class="form-container">
                <h3>💖 测试你们的姻缘契合度</h3>
                <p>红线千里一线牵，天作之合配良缘</p>
                
                <form id="marriageForm">
                    <div class="couple-forms">
                        <!-- 男方信息 -->
                        <div class="person-form male">
                            <h4>👨 男方信息</h4>
                            
                            <div class="form-group">
                                <label>姓名</label>
                                <input type="text" name="maleName" placeholder="请输入男方姓名" required>
                            </div>
                            
                            <div class="form-group">
                                <label>请选择您的农历出生日期</label>
                                <div class="marriage-date-picker male-date-picker" id="maleDatePicker" onclick="openMarriageDatePicker('male')">
                                    <div class="date-input-display">
                                        <span class="date-placeholder" id="maleDateDisplayText">请选择日期</span>
                                        <div class="date-picker-btn">
                                            <span class="picker-icon">📅</span>
                                        </div>
                                    </div>
                                </div>
                                <!-- 隐藏的输入字段存储选中的值 -->
                                <input type="hidden" name="maleBirthDate" id="maleBirthDate" required>
                                <input type="hidden" id="maleSelectedYear" name="maleSelectedYear">
                                <input type="hidden" id="maleSelectedMonth" name="maleSelectedMonth">
                                <input type="hidden" id="maleSelectedDay" name="maleSelectedDay">
                            </div>
                            
                            <div class="form-group">
                                <label>出生时辰</label>
                                <select name="maleBirthHour" required>
                                    <option value="">请选择时辰</option>
                                    <option value="zi">子时 (23:00-01:00)</option>
                                    <option value="chou">丑时 (01:00-03:00)</option>
                                    <option value="yin">寅时 (03:00-05:00)</option>
                                    <option value="mao">卯时 (05:00-07:00)</option>
                                    <option value="chen">辰时 (07:00-09:00)</option>
                                    <option value="si">巳时 (09:00-11:00)</option>
                                    <option value="wu">午时 (11:00-13:00)</option>
                                    <option value="wei">未时 (13:00-15:00)</option>
                                    <option value="shen">申时 (15:00-17:00)</option>
                                    <option value="you">酉时 (17:00-19:00)</option>
                                    <option value="xu">戌时 (19:00-21:00)</option>
                                    <option value="hai">亥时 (21:00-23:00)</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 女方信息 -->
                        <div class="person-form female">
                            <h4>👩 女方信息</h4>
                            
                            <div class="form-group">
                                <label>姓名</label>
                                <input type="text" name="femaleName" placeholder="请输入女方姓名" required>
                            </div>
                            
                            <div class="form-group">
                                <label>请选择您的农历出生日期</label>
                                <div class="marriage-date-picker female-date-picker" id="femaleDatePicker" onclick="openMarriageDatePicker('female')">
                                    <div class="date-input-display">
                                        <span class="date-placeholder" id="femaleDateDisplayText">请选择日期</span>
                                        <div class="date-picker-btn">
                                            <span class="picker-icon">📅</span>
                                        </div>
                                    </div>
                                </div>
                                <!-- 隐藏的输入字段存储选中的值 -->
                                <input type="hidden" name="femaleBirthDate" id="femaleBirthDate" required>
                                <input type="hidden" id="femaleSelectedYear" name="femaleSelectedYear">
                                <input type="hidden" id="femaleSelectedMonth" name="femaleSelectedMonth">
                                <input type="hidden" id="femaleSelectedDay" name="femaleSelectedDay">
                            </div>
                            
                            <div class="form-group">
                                <label>出生时辰</label>
                                <select name="femaleBirthHour" required>
                                    <option value="">请选择时辰</option>
                                    <option value="zi">子时 (23:00-01:00)</option>
                                    <option value="chou">丑时 (01:00-03:00)</option>
                                    <option value="yin">寅时 (03:00-05:00)</option>
                                    <option value="mao">卯时 (05:00-07:00)</option>
                                    <option value="chen">辰时 (07:00-09:00)</option>
                                    <option value="si">巳时 (09:00-11:00)</option>
                                    <option value="wu">午时 (11:00-13:00)</option>
                                    <option value="wei">未时 (13:00-15:00)</option>
                                    <option value="shen">申时 (15:00-17:00)</option>
                                    <option value="you">酉时 (17:00-19:00)</option>
                                    <option value="xu">戌时 (19:00-21:00)</option>
                                    <option value="hai">亥时 (21:00-23:00)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="love-divider">
                        <span class="heart">💕</span>
                    </div>
                    
                    <button type="submit" class="submit-btn">💖 开始合婚测算</button>
                </form>
            </div>
        </div>
    </div>

    <!-- AI分析结果区域 -->
    <div id="aiAnalysisSection" class="analysis-section" style="display: none;">
        <div class="analysis-header" style="display: none;">
            <h3>💕 八字合婚分析结果</h3>
            <div class="analysis-score">
                <span class="score-label">契合度：</span>
                <span id="marriageScore" class="score-value">--</span>
                <span class="score-unit">分</span>
            </div>
        </div>

        <div class="couple-display" style="display: none;">
            <div class="person-info male">
                <div class="person-avatar">👨</div>
                <div class="person-name" id="maleNameDisplay">--</div>
                <div class="person-bazi" id="maleBaziDisplay">--</div>
            </div>
            <div class="match-symbol">
                <div class="match-line"></div>
                <div class="match-heart">💖</div>
                <div class="match-level" id="matchLevel">--</div>
            </div>
            <div class="person-info female">
                <div class="person-avatar">👩</div>
                <div class="person-name" id="femaleNameDisplay">--</div>
                <div class="person-bazi" id="femaleBaziDisplay">--</div>
            </div>
        </div>

        <div class="analysis-summary">
            <h4>📝 合婚概述</h4>
            <div id="analysisSummary" class="summary-content">正在分析中...</div>
        </div>

        <div class="analysis-tabs">
            <div class="tab-buttons">
                <button class="tab-btn active" data-tab="overall">总体评价</button>
                <button class="tab-btn" data-tab="elements">五行喜忌</button>
                <button class="tab-btn" data-tab="personality">性格互动</button>
                <button class="tab-btn" data-tab="life">婚后生活</button>
                <button class="tab-btn" data-tab="children">子女缘分</button>
                <button class="tab-btn" data-tab="career">事业互助</button>
                <button class="tab-btn" data-tab="advice">婚姻建议</button>
            </div>
            
            <div class="tab-contents">
                <div id="tab-overall" class="tab-content active">分析中...</div>
                <div id="tab-elements" class="tab-content">分析中...</div>
                <div id="tab-personality" class="tab-content">分析中...</div>
                <div id="tab-life" class="tab-content">分析中...</div>
                <div id="tab-children" class="tab-content">分析中...</div>
                <div id="tab-career" class="tab-content">分析中...</div>
                <div id="tab-advice" class="tab-content">分析中...</div>
            </div>
        </div>

        <div class="recommendations-section">
            <h4>💡 专业建议</h4>
            <div id="recommendationsList" class="recommendations-list">
                <div class="recommendation-item">正在生成建议...</div>
            </div>
        </div>

        <div class="analysis-footer">
            <div class="analysis-info" style="display: none;">
                <span class="analysis-method">分析方式：<span id="analysisMethod">--</span></span>
                <span class="analysis-time">分析时间：<span id="analysisTime">--</span></span>
            </div>
            <button class="regenerate-btn" onclick="regenerateAnalysis()">🔄 重新分析</button>
        </div>
    </div>

    <style>
        .analysis-section {
            max-width: 800px;
            margin: 20px auto;
            padding: 25px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border: 1px solid rgba(255, 182, 193, 0.3);
        }

        .analysis-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(255, 20, 147, 0.3);
        }

        .analysis-header h3 {
            color: #DC143C;
            margin: 0;
            text-shadow: 0 0 10px rgba(220, 20, 60, 0.5);
        }

        .analysis-score {
            color: #8B0000;
        }

        .score-value {
            font-size: 24px;
            font-weight: bold;
            color: #DC143C;
            text-shadow: 0 0 10px rgba(220, 20, 60, 0.5);
        }

        .couple-display {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 25px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            border: 2px solid rgba(255, 20, 147, 0.3);
        }

        .person-info {
            text-align: center;
            flex: 1;
            padding: 15px;
        }

        .person-info.male {
            border-right: 1px solid rgba(70, 130, 180, 0.3);
        }

        .person-info.female {
            border-left: 1px solid rgba(255, 20, 147, 0.3);
        }

        .person-avatar {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .person-name {
            font-weight: bold;
            color: #8B0000;
            margin-bottom: 5px;
        }

        .person-bazi {
            font-size: 14px;
            color: #666;
        }

        .match-symbol {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0 20px;
        }

        .match-heart {
            font-size: 28px;
            margin: 10px 0;
            animation: heartBeat 2s infinite;
        }

        .match-level {
            font-weight: bold;
            color: #DC143C;
        }

        .match-line {
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, rgba(70, 130, 180, 0.5), rgba(255, 20, 147, 0.5));
            margin: 5px 0;
        }

        .analysis-summary {
            margin-bottom: 25px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            border: 2px solid rgba(255, 20, 147, 0.3);
        }

        .analysis-summary h4 {
            color: #DC143C;
            margin-bottom: 15px;
        }

        .summary-content {
            color: #8B0000;
            line-height: 1.6;
        }

        .analysis-tabs {
            margin-bottom: 25px;
        }

        .tab-buttons {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 5px;
            margin-bottom: 20px;
        }

        .tab-btn {
            padding: 10px 5px;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 20, 147, 0.3);
            border-radius: 8px;
            color: #8B0000;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            text-align: center;
        }

        .tab-btn.active,
        .tab-btn:hover {
            background: linear-gradient(135deg, #DC143C, #FF1493);
            color: white;
            border-color: #FF1493;
        }

        .tab-contents {
            min-height: 200px;
        }

        .tab-content {
            display: none;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            border: 2px solid rgba(255, 20, 147, 0.3);
            color: #8B0000;
            line-height: 1.6;
        }

        .tab-content.active {
            display: block;
        }

        .recommendations-section {
            margin-bottom: 25px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            border: 2px solid rgba(255, 20, 147, 0.3);
        }

        .recommendations-section h4 {
            color: #DC143C;
            margin-bottom: 15px;
        }

        .recommendations-list {
            display: grid;
            gap: 10px;
        }

        .recommendation-item {
            padding: 12px 15px;
            background: rgba(255, 182, 193, 0.2);
            border-left: 4px solid #FF1493;
            border-radius: 5px;
            color: #8B0000;
        }

        .analysis-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid rgba(255, 20, 147, 0.3);
        }

        .analysis-info {
            display: flex;
            gap: 20px;
            color: #8B0000;
            font-size: 14px;
        }

        .regenerate-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #DC143C, #FF1493);
            border: none;
            border-radius: 20px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .regenerate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 20, 60, 0.4);
        }

        .loading-animation {
            text-align: center;
            padding: 40px;
            color: #DC143C;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 20, 147, 0.2);
            border-top: 4px solid #FF1493;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .tab-buttons {
                grid-template-columns: repeat(4, 1fr);
            }
            
            .couple-display {
                flex-direction: column;
                gap: 15px;
            }
            
            .person-info.male {
                border-right: none;
                border-bottom: 1px solid rgba(70, 130, 180, 0.3);
            }
            
            .person-info.female {
                border-left: none;
                border-top: 1px solid rgba(255, 20, 147, 0.3);
            }
            
            .match-symbol {
                transform: rotate(90deg);
                margin: 15px 0;
            }
            
            .analysis-footer {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>

    <script src="../../js/modules/ai-service.js"></script>
    <script src="../../js/modules/marriage-ai.js"></script>
    <script>
        console.log('💕 八字合婚页面已加载');
        
        let marriageAI = null;
        let currentAnalysis = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            window.unifiedAuthService = new UnifiedAuthService();
            window.memberService = new MemberService();
            window.orderPaymentManager = new OrderPaymentManager();
            initializeMarriageAI();
            setupEventListeners();
        });

        // 初始化合婚AI
        function initializeMarriageAI() {
            try {
                // 初始化AI服务
                window.aiService = new AIService({
                    SERVICE_TYPE: 'deepseek',
                    DEEPSEEK_API_KEY: '***********************************'
                });
                console.log('✅ AI服务初始化成功');
                
                // 初始化合婚AI
                marriageAI = new MarriageAnalysisAI();
                console.log('✅ 合婚AI初始化成功');
                
                // 初始化认证服务
                window.unifiedAuthService = new UnifiedAuthService();
                window.memberService = new MemberService();
                window.orderPaymentManager = new OrderPaymentManager();
                console.log('✅ 认证服务初始化成功');
            } catch (error) {
                console.error('❌ 合婚AI初始化失败:', error);
            }
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 表单提交
            document.getElementById('marriageForm').addEventListener('submit', handleFormSubmit);
            
            // 标签页切换
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    switchTab(this.dataset.tab);
                });
            });
        }

        // 处理表单提交
        async function handleFormSubmit(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const maleData = {
                userName: formData.get('maleName'),
                birthDate: formData.get('maleBirthDate'),
                birthHour: formData.get('maleBirthHour')
            };
            
            const femaleData = {
                userName: formData.get('femaleName'),
                birthDate: formData.get('femaleBirthDate'),
                birthHour: formData.get('femaleBirthHour')
            };

            // 验证表单数据
            if (!maleData.userName || !maleData.birthDate || !maleData.birthHour ||
                !femaleData.userName || !femaleData.birthDate || !femaleData.birthHour) {
                alert('请填写完整的男女双方信息！');
                return;
            }

            // 服务配置
            const serviceConfig = {
                type: 'marriage',
                name: '八字合婚',
                price: 29.9,
                description: '深度分析男女八字合婚，预测婚姻运势'
            };

            // 合并用户数据
            const userData = {
                male: maleData,
                female: femaleData
            };

            // 创建订单并支付
            try {
                await window.orderPaymentManager.createOrderAndPay(
                    serviceConfig,
                    userData,
                    // 支付成功回调
                    async function(order, paymentResult) {
                        console.log('支付成功，开始AI分析');
                        await performMarriageAnalysis(maleData, femaleData);
                    },
                    // 取消支付回调
                    function(order) {
                        console.log('用户取消支付');
                    }
                );
            } catch (error) {
                console.error('订单创建失败:', error);
                alert('创建订单失败，请稍后重试');
            }
        }

        // 执行八字合婚分析
        async function performMarriageAnalysis(maleData, femaleData) {
            try {
                console.log('🚀 开始八字合婚分析...', maleData, femaleData);
                
                // 显示分析区域和加载动画
                showAnalysisSection();
                showLoadingAnimation();

                // 调用AI分析
                const result = await marriageAI.analyzeMarriageWithAI(maleData, femaleData);
                
                if (result && result.success) {
                    currentAnalysis = result.marriageAnalysis;
                    displayAnalysisResults(result.marriageAnalysis);
                    console.log('✅ 合婚分析完成');
                } else {
                    throw new Error('分析失败');
                }
                
            } catch (error) {
                console.error('❌ 合婚分析失败:', error);
                showErrorMessage('分析过程中出现错误，请稍后重试');
            }
        }

        // 显示分析区域
        function showAnalysisSection() {
            const section = document.getElementById('aiAnalysisSection');
            section.style.display = 'block';
            section.scrollIntoView({ behavior: 'smooth' });
        }

        // 显示加载动画
        function showLoadingAnimation() {
            const content = `
                <div class="loading-animation">
                    <div class="loading-spinner"></div>
                    <div>💕 AI正在分析您们的八字合婚...</div>
                    <div style="margin-top: 10px; font-size: 14px; color: #8B0000;">
                        正在计算天干地支，分析五行配置...
                    </div>
                </div>
            `;
            
            document.getElementById('analysisSummary').innerHTML = content;
            
            // 清空其他区域
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.innerHTML = '<div class="loading-animation"><div class="loading-spinner"></div><div>正在分析中...</div></div>';
            });
            
            document.getElementById('recommendationsList').innerHTML = '<div class="recommendation-item">正在生成专业建议...</div>';
        }

        // 显示分析结果
        function displayAnalysisResults(analysis) {
            console.log('📊 显示分析结果:', analysis);

            // 显示评分
            document.getElementById('marriageScore').textContent = analysis.compatibility.totalScore || '--';
            document.getElementById('matchLevel').textContent = analysis.compatibility.marriageLevel.level || '--';

            // 显示男女信息
            document.getElementById('maleNameDisplay').textContent = analysis.maleData.userName;
            document.getElementById('femaleNameDisplay').textContent = analysis.femaleData.userName;
            
            // 显示八字
            const maleBazi = `${analysis.maleBazi.year.gan}${analysis.maleBazi.year.zhi} ${analysis.maleBazi.month.gan}${analysis.maleBazi.month.zhi} ${analysis.maleBazi.day.gan}${analysis.maleBazi.day.zhi} ${analysis.maleBazi.hour.gan}${analysis.maleBazi.hour.zhi}`;
            const femaleBazi = `${analysis.femaleBazi.year.gan}${analysis.femaleBazi.year.zhi} ${analysis.femaleBazi.month.gan}${analysis.femaleBazi.month.zhi} ${analysis.femaleBazi.day.gan}${analysis.femaleBazi.day.zhi} ${analysis.femaleBazi.hour.gan}${analysis.femaleBazi.hour.zhi}`;
            
            document.getElementById('maleBaziDisplay').textContent = maleBazi;
            document.getElementById('femaleBaziDisplay').textContent = femaleBazi;

            // 显示概述
            document.getElementById('analysisSummary').innerHTML = analysis.summary || '暂无概述';

            // 显示各个维度的分析
            displayAnalysisSections(analysis.sections);

            // 显示建议
            displayRecommendations(analysis.suggestions);

            // 显示分析信息
            updateAnalysisInfo(analysis);
        }

        // 显示分析章节
        function displayAnalysisSections(sections) {
            Object.keys(sections).forEach(key => {
                const element = document.getElementById(`tab-${key}`);
                if (element && sections[key]) {
                    element.innerHTML = sections[key];
                }
            });
        }

        // 显示建议
        function displayRecommendations(recommendations) {
            if (!recommendations || recommendations.length === 0) {
                document.getElementById('recommendationsList').innerHTML = '<div class="recommendation-item">暂无建议</div>';
                return;
            }

            const html = recommendations.map(rec => 
                `<div class="recommendation-item">${rec}</div>`
            ).join('');
            
            document.getElementById('recommendationsList').innerHTML = html;
        }

        // 更新分析信息
        function updateAnalysisInfo(analysis) {
            document.getElementById('analysisMethod').textContent = analysis.method === 'ai' ? 'AI智能分析' : '本地分析';
            
            if (analysis.timestamp) {
                const time = new Date(analysis.timestamp).toLocaleString('zh-CN');
                document.getElementById('analysisTime').textContent = time;
            }
        }

        // 切换标签页
        function switchTab(tabName) {
            // 更新按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // 更新内容显示
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`tab-${tabName}`).classList.add('active');
        }

        // 重新分析
        function regenerateAnalysis() {
            if (currentAnalysis) {
                console.log('🔄 重新生成分析...');
                showLoadingAnimation();
                
                // 模拟重新分析
                setTimeout(() => {
                    displayAnalysisResults(currentAnalysis);
                }, 2000);
            }
        }

        // 显示错误信息
        function showErrorMessage(message) {
            const content = `
                <div style="text-align: center; padding: 40px; color: #FF6B6B;">
                    <div style="font-size: 48px; margin-bottom: 20px;">❌</div>
                    <div style="font-size: 18px; margin-bottom: 10px;">分析失败</div>
                    <div style="color: #8B0000;">${message}</div>
                </div>
            `;
            
            document.getElementById('analysisSummary').innerHTML = content;
        }

        // ====== 婚姻日期选择器功能 ======

        // 婚姻日期选择器变量
        let currentYear = new Date().getFullYear();
        let currentMonth = new Date().getMonth() + 1;
        let selectedDate = null;
        let currentGender = 'male'; // 当前选择的是男方还是女方
        let maleSelectedDate = null;
        let femaleSelectedDate = null;
        // 年份选择器变量
        let yearRangeStart = Math.floor(currentYear / 10) * 10;

        // 在页面初始化时添加日期选择器初始化
        document.addEventListener('DOMContentLoaded', function() {
            initMarriageDatePicker();
        });

        // 初始化婚姻日期选择器
        function initMarriageDatePicker() {
            try {
                const today = new Date();
                currentYear = today.getFullYear();
                currentMonth = today.getMonth() + 1;
                
                // 添加点击背景关闭功能
                const modal = document.getElementById('marriageDateModal');
                if (modal) {
                    modal.addEventListener('click', function(e) {
                        if (e.target === modal) {
                            closeMarriageDatePicker();
                        }
                    });
                }
                
                console.log('🗓️ 婚姻日期选择器初始化完成');
            } catch (error) {
                console.error('❌ 婚姻日期选择器初始化失败:', error);
            }
        }

        // 打开婚姻日期选择器
        function openMarriageDatePicker(gender) {
            currentGender = gender;
            const modal = document.getElementById('marriageDateModal');
            const modalHeader = document.querySelector('.marriage-modal-header');
            const modalIcon = document.querySelector('.header-icon');
            const modalTitle = document.querySelector('.marriage-modal-header h3');
            
            if (modal) {
                modal.style.display = 'flex';
                
                // 根据性别调整模态框样式和内容
                if (gender === 'male') {
                    modalHeader.classList.add('male');
                    modalIcon.textContent = '👨';
                    modalTitle.textContent = '请选择男方农历出生日期';
                    selectedDate = maleSelectedDate;
                } else {
                    modalHeader.classList.remove('male');
                    modalIcon.textContent = '👩';
                    modalTitle.textContent = '请选择女方农历出生日期';
                    selectedDate = femaleSelectedDate;
                }
                
                // 生成日历
                generateCalendar();
                updateMonthDisplay();
                updateModalDateDisplay();
                
                console.log(`📅 ${gender === 'male' ? '男方' : '女方'}日期选择器已打开`);
            }
        }

        // 关闭婚姻日期选择器
        function closeMarriageDatePicker() {
            const modal = document.getElementById('marriageDateModal');
            
            if (modal) {
                modal.style.display = 'none';
                
                console.log('📅 婚姻日期选择器已关闭');
            }
        }

        // 上一个月
        function prevMonth() {
            currentMonth--;
            if (currentMonth < 1) {
                currentMonth = 12;
                currentYear--;
            }
            generateCalendar();
            updateMonthDisplay();
        }

        // 下一个月
        function nextMonth() {
            currentMonth++;
            if (currentMonth > 12) {
                currentMonth = 1;
                currentYear++;
            }
            generateCalendar();
            updateMonthDisplay();
        }

        // 生成日历
        function generateCalendar() {
            const calendarGrid = document.getElementById('calendarGrid');
            if (!calendarGrid) return;
            
            calendarGrid.innerHTML = '';
            
            // 获取当前月份第一天
            const firstDay = new Date(currentYear, currentMonth - 1, 1);
            // 获取当前月份最后一天
            const lastDay = new Date(currentYear, currentMonth, 0);
            // 获取上个月最后一天
            const prevLastDay = new Date(currentYear, currentMonth - 1, 0);
            
            // 计算第一天是星期几（0=周日，1=周一...）
            let firstDayOfWeek = firstDay.getDay();
            if (firstDayOfWeek === 0) firstDayOfWeek = 7; // 调整为周一开始
            
            // 添加上个月的日期
            for (let i = firstDayOfWeek - 1; i > 0; i--) {
                const day = prevLastDay.getDate() - i + 1;
                const dayElement = createDayElement(day, true, currentMonth === 1 ? currentYear - 1 : currentYear, currentMonth === 1 ? 12 : currentMonth - 1);
                calendarGrid.appendChild(dayElement);
            }
            
            // 添加当前月份的日期
            for (let day = 1; day <= lastDay.getDate(); day++) {
                const dayElement = createDayElement(day, false, currentYear, currentMonth);
                calendarGrid.appendChild(dayElement);
            }
            
            // 添加下个月的日期（填满42格）
            const totalCells = 42;
            const filledCells = calendarGrid.children.length;
            const remainingCells = totalCells - filledCells;
            
            for (let day = 1; day <= remainingCells; day++) {
                const dayElement = createDayElement(day, true, currentMonth === 12 ? currentYear + 1 : currentYear, currentMonth === 12 ? 1 : currentMonth + 1);
                calendarGrid.appendChild(dayElement);
            }
        }

        // 创建日期元素
        function createDayElement(day, isOtherMonth, year, month) {
            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day';
            dayElement.textContent = day;
            dayElement.dataset.year = year;
            dayElement.dataset.month = month;
            dayElement.dataset.day = day;
            
            if (isOtherMonth) {
                dayElement.classList.add('other-month');
            }
            
            // 检查是否为今天
            const today = new Date();
            if (year === today.getFullYear() && month === today.getMonth() + 1 && day === today.getDate()) {
                dayElement.classList.add('today');
            }
            
            // 检查是否为选中日期
            if (selectedDate && selectedDate.year === year && selectedDate.month === month && selectedDate.day === day) {
                dayElement.classList.add('selected');
            }
            
            // 添加点击事件
            dayElement.addEventListener('click', () => {
                selectDate(year, month, day);
            });
            
            return dayElement;
        }

        // 更新月份显示
        function updateMonthDisplay() {
            const monthDisplay = document.getElementById('currentMonthDisplay');
            if (monthDisplay) {
                monthDisplay.textContent = `${currentYear}年${currentMonth.toString().padStart(2, '0')}月`;
            }
        }

        // 选择日期
        function selectDate(year, month, day) {
            selectedDate = { year, month, day };
            
            // 根据当前性别保存到对应变量
            if (currentGender === 'male') {
                maleSelectedDate = selectedDate;
                
                // 更新男方隐藏字段
                const maleBirthDate = document.getElementById('maleBirthDate');
                if (maleBirthDate) {
                    maleBirthDate.value = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                }
                
                // 更新男方显示
                updateDateDisplay('male');
            } else {
                femaleSelectedDate = selectedDate;
                
                // 更新女方隐藏字段
                const femaleBirthDate = document.getElementById('femaleBirthDate');
                if (femaleBirthDate) {
                    femaleBirthDate.value = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                }
                
                // 更新女方显示
                updateDateDisplay('female');
            }
            
            // 更新模态框显示
            updateModalDateDisplay();
            
            // 重新生成日历以更新选中状态
            generateCalendar();
            
            // 延迟关闭模态框
            setTimeout(() => {
                closeMarriageDatePicker();
            }, 300);
        }

        // 清除日期
        function clearDate() {
            selectedDate = null;
            
            if (currentGender === 'male') {
                maleSelectedDate = null;
                const maleBirthDate = document.getElementById('maleBirthDate');
                if (maleBirthDate) maleBirthDate.value = '';
                updateDateDisplay('male');
            } else {
                femaleSelectedDate = null;
                const femaleBirthDate = document.getElementById('femaleBirthDate');
                if (femaleBirthDate) femaleBirthDate.value = '';
                updateDateDisplay('female');
            }
            
            updateModalDateDisplay();
            generateCalendar();
            
            setTimeout(() => {
                closeMarriageDatePicker();
            }, 300);
        }

        // 选择今天
        function selectToday() {
            const today = new Date();
            const year = today.getFullYear();
            const month = today.getMonth() + 1;
            const day = today.getDate();
            
            currentYear = year;
            currentMonth = month;
            
            selectDate(year, month, day);
            updateMonthDisplay();
            generateCalendar();
        }

        // 确认日期选择
        function confirmDate() {
            if (selectedDate) {
                closeMarriageDatePicker();
            } else {
                alert('请先选择一个日期');
            }
        }

        // 更新日期显示（主界面）
        function updateDateDisplay(gender) {
            const dateDisplayText = document.getElementById(gender + 'DateDisplayText');
            const targetDate = gender === 'male' ? maleSelectedDate : femaleSelectedDate;
            
            if (dateDisplayText) {
                if (targetDate) {
                    const { year, month, day } = targetDate;
                    dateDisplayText.textContent = `${year}/${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`;
                    dateDisplayText.classList.add('selected');
                } else {
                    dateDisplayText.textContent = '请选择日期';
                    dateDisplayText.classList.remove('selected');
                }
            }
        }

        // 更新模态框日期显示
        function updateModalDateDisplay() {
            const modalDateDisplay = document.getElementById('modalDateDisplay');
            if (modalDateDisplay) {
                if (selectedDate) {
                    const { year, month, day } = selectedDate;
                    modalDateDisplay.textContent = `${year}/${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`;
                    modalDateDisplay.classList.add('selected');
                } else {
                    modalDateDisplay.textContent = '请选择日期';
                    modalDateDisplay.classList.remove('selected');
                }
            }
        }

        // 显示年份选择器
        function showYearSelector() {
            const yearSelector = document.getElementById('yearSelector');
            const calendarContainer = document.querySelector('.calendar-container');
            const calendarFooter = document.querySelector('.calendar-footer');
            const monthNavigation = document.querySelector('.month-navigation');
            
            if (yearSelector) {
                yearSelector.style.display = 'block';
                if (calendarContainer) calendarContainer.style.display = 'none';
                if (calendarFooter) calendarFooter.style.display = 'none';
                if (monthNavigation) monthNavigation.style.display = 'none';
                
                generateYearGrid();
                updateYearRangeDisplay();
            }
        }

        // 隐藏年份选择器
        function hideYearSelector() {
            const yearSelector = document.getElementById('yearSelector');
            const calendarContainer = document.querySelector('.calendar-container');
            const calendarFooter = document.querySelector('.calendar-footer');
            const monthNavigation = document.querySelector('.month-navigation');
            
            if (yearSelector) {
                yearSelector.style.display = 'none';
                if (calendarContainer) calendarContainer.style.display = 'block';
                if (calendarFooter) calendarFooter.style.display = 'flex';
                if (monthNavigation) monthNavigation.style.display = 'flex';
            }
        }

        // 改变年份范围
        function changeYearRange(delta) {
            yearRangeStart += delta;
            if (yearRangeStart < 1900) yearRangeStart = 1900;
            if (yearRangeStart > 2100) yearRangeStart = 2100;
            
            generateYearGrid();
            updateYearRangeDisplay();
        }

        // 更新年份范围显示
        function updateYearRangeDisplay() {
            const yearRangeDisplay = document.getElementById('yearRangeDisplay');
            if (yearRangeDisplay) {
                yearRangeDisplay.textContent = `${yearRangeStart}-${yearRangeStart + 9}`;
            }
        }

        // 生成年份网格
        function generateYearGrid() {
            const yearGrid = document.getElementById('yearGrid');
            if (!yearGrid) return;
            
            yearGrid.innerHTML = '';
            const currentYearValue = new Date().getFullYear();
            
            for (let i = 0; i < 10; i++) {
                const year = yearRangeStart + i;
                const yearItem = document.createElement('div');
                yearItem.className = 'year-item';
                yearItem.textContent = year;
                yearItem.dataset.year = year;
                
                // 添加当前年份标记
                if (year === currentYearValue) {
                    yearItem.classList.add('current');
                }
                
                // 添加选中年份标记
                if (year === currentYear) {
                    yearItem.classList.add('selected');
                }
                
                // 添加点击事件
                yearItem.addEventListener('click', () => {
                    selectYear(year);
                });
                
                yearGrid.appendChild(yearItem);
            }
        }

        // 选择年份
        function selectYear(year) {
            currentYear = year;
            
            // 更新月份显示
            updateMonthDisplay();
            
            // 重新生成日历
            generateCalendar();
            
            // 隐藏年份选择器
            hideYearSelector();
        }

        // 表单提交处理
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('marriageForm');
            if (form) {
                form.addEventListener('submit', handleFormSubmit);
            }
        });

        // 验证表单数据
        function validateFormData(userData) {
            if (!userData.maleName || userData.maleName.trim() === '') {
                alert('请输入男方姓名');
                return false;
            }
            
            if (!userData.maleBirthDate) {
                alert('请选择男方出生日期');
                return false;
            }
            
            if (!userData.maleBirthHour) {
                alert('请选择男方出生时辰');
                return false;
            }
            
            if (!userData.femaleName || userData.femaleName.trim() === '') {
                alert('请输入女方姓名');
                return false;
            }
            
            if (!userData.femaleBirthDate) {
                alert('请选择女方出生日期');
                return false;
            }
            
            if (!userData.femaleBirthHour) {
                alert('请选择女方出生时辰');
                return false;
            }
            
            return true;
        }

        // 显示分析结果
        function showAnalysisResult(result) {
            // 创建结果弹窗
            const resultModal = document.createElement('div');
            resultModal.className = 'result-modal-overlay';
            resultModal.innerHTML = `
                <div class="result-modal">
                    <div class="result-header">
                        <h3>💕 八字合婚结果</h3>
                        <button class="close-btn" onclick="this.parentElement.parentElement.parentElement.remove()">×</button>
                    </div>
                    <div class="result-content">
                        <div class="analysis-summary">
                            <h4>合婚摘要</h4>
                            <p>${result.summary || '基于两人的生辰八字进行合婚分析'}</p>
                        </div>
                        <div class="analysis-sections">
                            ${result.sections ? Object.entries(result.sections).map(([title, content]) => `
                                <div class="analysis-section">
                                    <h5>${title}</h5>
                                    <p>${content}</p>
                                </div>
                            `).join('') : ''}
                        </div>
                        <div class="compatibility-score">
                            <h4>匹配指数</h4>
                            <div class="score-display">
                                <span class="score-value">${result.compatibilityScore || 85}</span>
                                <span class="score-label">分</span>
                            </div>
                        </div>
                    </div>
                    <div class="result-actions">
                        <button class="btn btn-primary" onclick="downloadResult()">保存结果</button>
                        <button class="btn btn-secondary" onclick="this.parentElement.parentElement.parentElement.remove()">关闭</button>
                    </div>
                </div>
            `;

            // 添加结果弹窗样式
            if (!document.getElementById('resultModalStyles')) {
                const styles = `
                    <style id="resultModalStyles">
                        .result-modal-overlay {
                            position: fixed;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: rgba(0, 0, 0, 0.8);
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            z-index: 10000;
                        }

                        .result-modal {
                            background: white;
                            border-radius: 15px;
                            max-width: 90%;
                            max-height: 90%;
                            overflow-y: auto;
                            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                        }

                        .result-header {
                            background: linear-gradient(135deg, #DC143C, #FF1493);
                            color: white;
                            padding: 20px;
                            border-radius: 15px 15px 0 0;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        }

                        .result-header h3 {
                            margin: 0;
                            font-size: 20px;
                        }

                        .close-btn {
                            background: none;
                            border: none;
                            color: white;
                            font-size: 24px;
                            cursor: pointer;
                            padding: 0;
                            width: 30px;
                            height: 30px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }

                        .result-content {
                            padding: 20px;
                        }

                        .analysis-summary {
                            margin-bottom: 20px;
                            padding: 15px;
                            background: #f8f9fa;
                            border-radius: 10px;
                        }

                        .analysis-summary h4 {
                            color: #DC143C;
                            margin: 0 0 10px 0;
                        }

                        .analysis-sections {
                            margin-bottom: 20px;
                        }

                        .analysis-section {
                            margin-bottom: 15px;
                            padding: 15px;
                            border-left: 4px solid #DC143C;
                            background: #f8f9fa;
                            border-radius: 0 10px 10px 0;
                        }

                        .analysis-section h5 {
                            color: #DC143C;
                            margin: 0 0 10px 0;
                            font-size: 16px;
                        }

                        .analysis-section p {
                            margin: 0;
                            line-height: 1.6;
                            color: #333;
                        }

                        .compatibility-score {
                            text-align: center;
                            margin: 20px 0;
                        }

                        .compatibility-score h4 {
                            color: #DC143C;
                            margin: 0 0 15px 0;
                        }

                        .score-display {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            gap: 10px;
                        }

                        .score-value {
                            font-size: 48px;
                            font-weight: bold;
                            color: #DC143C;
                        }

                        .score-label {
                            font-size: 24px;
                            color: #666;
                        }

                        .result-actions {
                            padding: 20px;
                            border-top: 1px solid #eee;
                            display: flex;
                            gap: 10px;
                            justify-content: center;
                        }

                        .btn {
                            padding: 10px 20px;
                            border: none;
                            border-radius: 8px;
                            cursor: pointer;
                            font-size: 14px;
                            transition: all 0.3s ease;
                        }

                        .btn-primary {
                            background: linear-gradient(135deg, #DC143C, #FF1493);
                            color: white;
                        }

                        .btn-secondary {
                            background: #f8f9fa;
                            color: #666;
                            border: 1px solid #ddd;
                        }

                        .btn:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
                        }
                    </style>
                `;
                document.head.insertAdjacentHTML('beforeend', styles);
            }

            document.body.appendChild(resultModal);
        }

        // 下载结果
        function downloadResult() {
            alert('下载功能开发中...');
        }
    </script>

    <!-- 婚姻日期选择器模态框 -->
    <div id="marriageDateModal" class="marriage-date-modal">
        <div class="marriage-modal-container">
            <!-- 爱情主题头部 -->
            <div class="marriage-modal-header">
                <div class="header-icon">👩</div>
                <h3>请选择农历出生日期</h3>
            </div>
            
            <!-- 日期输入框 -->
            <div class="date-input-container">
                <div class="date-input-box">
                    <span class="date-value" id="modalDateDisplay">请选择日期</span>
                    <div class="date-confirm-btn" onclick="confirmDate()">
                        <span class="confirm-icon">✓</span>
                    </div>
                </div>
            </div>
            
            <!-- 月份导航 -->
            <div class="month-navigation">
                <button class="nav-btn prev-month" onclick="prevMonth()">
                    <span>↑</span>
                </button>
                <div class="current-month" onclick="showYearSelector()">
                    <span id="currentMonthDisplay">2025年01月</span>
                    <span class="month-dropdown">▼</span>
                </div>
                <button class="nav-btn next-month" onclick="nextMonth()">
                    <span>↓</span>
                </button>
            </div>

            <!-- 年份选择器 -->
            <div id="yearSelector" class="year-selector" style="display: none;">
                <div class="year-selector-header">
                    <button class="year-nav-btn" onclick="changeYearRange(-10)">‹‹</button>
                    <span id="yearRangeDisplay">2020-2029</span>
                    <button class="year-nav-btn" onclick="changeYearRange(10)">››</button>
                </div>
                <div class="year-grid" id="yearGrid">
                    <!-- 年份选项将通过JavaScript生成 -->
                </div>
                <div class="year-selector-footer">
                    <button class="year-btn" onclick="hideYearSelector()">返回</button>
                </div>
            </div>
            
            <!-- 日历网格 -->
            <div class="calendar-container">
                <!-- 星期标题 -->
                <div class="week-header">
                    <div class="week-day">一</div>
                    <div class="week-day">二</div>
                    <div class="week-day">三</div>
                    <div class="week-day">四</div>
                    <div class="week-day">五</div>
                    <div class="week-day">六</div>
                    <div class="week-day">日</div>
                </div>
                
                <!-- 日历日期网格 -->
                <div class="calendar-grid" id="calendarGrid">
                    <!-- 日期将通过JavaScript动态生成 -->
                </div>
            </div>
            
            <!-- 底部按钮 -->
            <div class="calendar-footer">
                <button class="footer-btn clear-btn" onclick="clearDate()">清除</button>
                <button class="footer-btn today-btn" onclick="selectToday()">今天</button>
            </div>
            
            <!-- 收起箭头 -->
            <div class="collapse-arrow" onclick="closeMarriageDatePicker()">
                <span>▼</span>
            </div>
        </div>
    </div>
</body>
</html> 