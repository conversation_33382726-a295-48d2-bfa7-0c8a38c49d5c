/**
 * 统一认证服务模块
 * 提供管理员和会员的统一认证功能
 */

class UnifiedAuthService {
    constructor() {
        this.baseUrl = '/api';
        this.tokenKey = 'unified_token';
        this.userTypeKey = 'user_type';
        this.userInfoKey = 'user_info';
        this.currentUser = null;
    }

    /**
     * 统一登录入口
     * @param {string} userName - 用户名
     * @param {string} password - 密码
     * @param {string} userType - 用户类型 (admin/member)
     * @returns {Promise<Object>} 登录结果
     */
    async unifiedLogin(userName, password, userType) {
        try {
            const response = await fetch(`${this.baseUrl}/unified/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    userName: userName,
                    password: password,
                    userType: userType
                })
            });

            const result = await response.json();
            
            if (result.code === 200) {
                // 保存认证信息
                localStorage.setItem(this.tokenKey, result.data.token);
                localStorage.setItem(this.userTypeKey, userType);
                
                // 如果是会员登录，先保存用户名到本地存储
                if (userType === 'member') {
                    const tempUserInfo = { userName: userName };
                    localStorage.setItem(this.userInfoKey, JSON.stringify(tempUserInfo));
                }
                
                // 登录成功后立即获取用户信息
                try {
                    const userInfoResult = await this.getUserInfo();
                    if (userInfoResult.success) {
                        this.currentUser = userInfoResult.data;
                        // 触发登录成功事件
                        this.dispatchLoginSuccessEvent(userInfoResult.data);
                    }
                } catch (error) {
                    console.warn('获取用户信息失败，但登录成功:', error);
                }
                
                return {
                    success: true,
                    data: result.data,
                    message: '登录成功'
                };
            } else {
                return {
                    success: false,
                    message: result.message || '登录失败'
                };
            }
        } catch (error) {
            console.error('统一登录失败:', error);
            return {
                success: false,
                message: '网络错误，请稍后重试'
            };
        }
    }

    /**
     * 管理员登录
     * @param {string} userName - 用户名
     * @param {string} password - 密码
     * @returns {Promise<Object>} 登录结果
     */
    async adminLogin(userName, password) {
        return this.unifiedLogin(userName, password, 'admin');
    }

    /**
     * 会员登录
     * @param {string} userName - 用户名
     * @param {string} password - 密码
     * @returns {Promise<Object>} 登录结果
     */
    async memberLogin(userName, password) {
        return this.unifiedLogin(userName, password, 'member');
    }

    /**
     * 统一退出登录
     * @param {string} userType - 用户类型
     * @returns {Promise<Object>} 退出结果
     */
    async unifiedLogout(userType) {
        try {
            const response = await fetch(`${this.baseUrl}/unified/auth/logout`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getToken()}`
                },
                body: JSON.stringify({
                    userType: userType
                })
            });

            const result = await response.json();
            
            // 清除本地存储
            this.clearAuth();
            this.currentUser = null;
            
            // 触发退出登录事件
            this.dispatchLogoutEvent();
            
            return {
                success: true,
                message: '退出登录成功'
            };
        } catch (error) {
            console.error('退出登录失败:', error);
            // 即使网络请求失败，也清除本地存储
            this.clearAuth();
            this.currentUser = null;
            this.dispatchLogoutEvent();
            return {
                success: true,
                message: '退出登录成功'
            };
        }
    }

    /**
     * 刷新Token
     * @param {string} refreshToken - 刷新Token
     * @param {string} userType - 用户类型
     * @returns {Promise<Object>} 刷新结果
     */
    async refreshToken(refreshToken, userType) {
        try {
            const response = await fetch(`${this.baseUrl}/unified/auth/refresh_token`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    refreshToken: refreshToken,
                    userType: userType
                })
            });

            const result = await response.json();
            
            if (result.code === 200) {
                // 更新token
                localStorage.setItem(this.tokenKey, result.data.token);
                
                return {
                    success: true,
                    data: result.data
                };
            } else {
                return {
                    success: false,
                    message: result.message || '刷新Token失败'
                };
            }
        } catch (error) {
            console.error('刷新Token失败:', error);
            return {
                success: false,
                message: '网络错误，请稍后重试'
            };
        }
    }

    /**
     * 获取当前Token
     * @returns {string|null} Token
     */
    getToken() {
        return localStorage.getItem(this.tokenKey);
    }

    /**
     * 获取当前用户类型
     * @returns {string|null} 用户类型
     */
    getUserType() {
        return localStorage.getItem(this.userTypeKey);
    }

    /**
     * 检查是否已登录
     * @returns {boolean} 是否已登录
     */
    isLoggedIn() {
        const token = this.getToken();
        const userType = this.getUserType();
        const currentUser = this.getCurrentUser();
        const hasUserInfo = currentUser !== null;
        
        console.log('🔍 统一认证服务登录状态检查:', {
            hasToken: !!token,
            tokenLength: token ? token.length : 0,
            hasUserType: !!userType,
            userType: userType,
            hasUserInfo: hasUserInfo,
            currentUser: currentUser,
            tokenPrefix: token ? token.substring(0, 10) + '...' : '无'
        });
        
        const isLoggedIn = !!(token && userType && hasUserInfo);
        console.log('📊 统一认证服务最终登录状态:', isLoggedIn);
        
        return isLoggedIn;
    }

    /**
     * 检查是否是管理员
     * @returns {boolean} 是否是管理员
     */
    isAdmin() {
        return this.getUserType() === 'admin';
    }

    /**
     * 检查是否是会员
     * @returns {boolean} 是否是会员
     */
    isMember() {
        return this.getUserType() === 'member';
    }

    /**
     * 清除认证信息
     */
    clearAuth() {
        localStorage.removeItem(this.tokenKey);
        localStorage.removeItem(this.userTypeKey);
        localStorage.removeItem(this.userInfoKey);
    }

    /**
     * 获取用户信息
     * @returns {Promise<Object>} 用户信息
     */
    async getUserInfo() {
        try {
            const userType = this.getUserType();
            let apiPath;
            let url;
            
            if (userType === 'admin') {
                apiPath = '/auth/user_info';
                url = `${this.baseUrl}${apiPath}`;
            } else if (userType === 'member') {
                // 会员接口需要userName参数，从本地存储获取
                const localUserInfo = this.getLocalUserInfo();
                const userName = localUserInfo ? localUserInfo.userName : null;
                
                if (!userName) {
                    console.error('无法获取用户名，请重新登录');
                    return {
                        success: false,
                        message: '无法获取用户名，请重新登录'
                    };
                }
                
                apiPath = '/member/info';
                url = `${this.baseUrl}${apiPath}?userName=${encodeURIComponent(userName)}`;
            } else {
                throw new Error('无效的用户类型');
            }

            console.log(`获取用户信息，API路径: ${apiPath}`);
            console.log(`完整URL: ${url}`);
            
            const response = await fetch(url, {
                headers: {
                    'Authorization': `Bearer ${this.getToken()}`
                }
            });

            const result = await response.json();
            
            if (result.code === 200) {
                // 保存用户信息到本地存储
                localStorage.setItem(this.userInfoKey, JSON.stringify(result.data));
                this.currentUser = result.data;
                console.log('用户信息获取成功:', result.data);
                return {
                    success: true,
                    data: result.data
                };
            } else {
                console.error('获取用户信息失败:', result.message);
                return {
                    success: false,
                    message: result.message || '获取用户信息失败'
                };
            }
        } catch (error) {
            console.error('获取用户信息失败:', error);
            return {
                success: false,
                message: '网络错误，请稍后重试'
            };
        }
    }

    /**
     * 获取本地存储的用户信息
     * @returns {Object|null} 用户信息
     */
    getLocalUserInfo() {
        const userInfo = localStorage.getItem(this.userInfoKey);
        if (userInfo) {
            try {
                this.currentUser = JSON.parse(userInfo);
                console.log('✅ 成功解析用户信息:', this.currentUser);
                return this.currentUser;
            } catch (error) {
                console.error('❌ 解析用户信息失败:', error, '原始数据:', userInfo);
                return null;
            }
        }
        console.log('⚠️ 本地存储中没有用户信息');
        return null;
    }

    /**
     * 获取当前用户信息（优先从内存获取）
     * @returns {Object|null} 用户信息
     */
    getCurrentUser() {
        if (this.currentUser) {
            return this.currentUser;
        }
        return this.getLocalUserInfo();
    }

    /**
     * 触发登录成功事件
     * @param {Object} userInfo - 用户信息
     */
    dispatchLoginSuccessEvent(userInfo) {
        const event = new CustomEvent('userLoginSuccess', {
            detail: {
                userInfo: userInfo,
                userType: this.getUserType(),
                timestamp: Date.now()
            }
        });
        window.dispatchEvent(event);
        console.log('触发登录成功事件:', userInfo);
    }

    /**
     * 触发退出登录事件
     */
    dispatchLogoutEvent() {
        const event = new CustomEvent('userLogout', {
            detail: {
                timestamp: Date.now()
            }
        });
        window.dispatchEvent(event);
        console.log('触发退出登录事件');
    }

    /**
     * 触发用户信息更新事件
     * @param {Object} userInfo - 用户信息
     */
    dispatchUserInfoUpdateEvent(userInfo) {
        const event = new CustomEvent('userInfoUpdate', {
            detail: {
                userInfo: userInfo,
                timestamp: Date.now()
            }
        });
        window.dispatchEvent(event);
        console.log('触发用户信息更新事件:', userInfo);
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedAuthService;
} 