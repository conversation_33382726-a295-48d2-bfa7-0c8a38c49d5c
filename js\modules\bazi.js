// 八字精批模块
class BaziModule {
    constructor() {
        this.modal = null;
        this.canvas = null;
        this.ctx = null;
        this.animationId = null;
        this.particles = [];
        this.init();
    }

    init() {
        this.modal = document.getElementById('baziModal');
        this.canvas = document.getElementById('baziCanvas');
        
        if (this.canvas) {
            this.ctx = this.canvas.getContext('2d');
            this.initCanvas();
            this.createParticles();
            this.animate();
        }

        this.initForm();
        this.bindEvents();
    }

    // 初始化Canvas
    initCanvas() {
        const resizeCanvas = () => {
            const rect = this.canvas.parentElement.getBoundingClientRect();
            this.canvas.width = rect.width;
            this.canvas.height = rect.height;
        };
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
    }

    // 创建粒子效果
    createParticles() {
        this.particles = [];
        const particleCount = 30;
        
        for (let i = 0; i < particleCount; i++) {
            this.particles.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                size: Math.random() * 3 + 1,
                speedX: (Math.random() - 0.5) * 0.5,
                speedY: (Math.random() - 0.5) * 0.5,
                opacity: Math.random() * 0.5 + 0.2,
                color: this.getRandomColor()
            });
        }
    }

    // 获取随机颜色
    getRandomColor() {
        const colors = [
            'rgba(255, 215, 0, ',    // 金色
            'rgba(220, 20, 60, ',    // 红色  
            'rgba(255, 99, 71, ',    // 橙红色
            'rgba(255, 165, 0, ',    // 橙色
            'rgba(138, 43, 226, '    // 紫色
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    // 绘制太极图
    drawTaiChi(x, y, radius, rotation) {
        this.ctx.save();
        this.ctx.translate(x, y);
        this.ctx.rotate(rotation);

        // 外圆 - 白色
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.beginPath();
        this.ctx.arc(0, 0, radius, 0, Math.PI * 2);
        this.ctx.fill();

        // 黑色半圆
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
        this.ctx.beginPath();
        this.ctx.arc(0, 0, radius, 0, Math.PI);
        this.ctx.fill();

        // 上半小圆（白）
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.beginPath();
        this.ctx.arc(0, -radius/2, radius/2, 0, Math.PI * 2);
        this.ctx.fill();

        // 下半小圆（黑）
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
        this.ctx.beginPath();
        this.ctx.arc(0, radius/2, radius/2, 0, Math.PI * 2);
        this.ctx.fill();

        // 上小圆内黑点
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
        this.ctx.beginPath();
        this.ctx.arc(0, -radius/2, radius/6, 0, Math.PI * 2);
        this.ctx.fill();

        // 下小圆内白点
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
        this.ctx.beginPath();
        this.ctx.arc(0, radius/2, radius/6, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.restore();
    }

    // 绘制八卦符号
    drawBagua(x, y, size, rotation) {
        this.ctx.save();
        this.ctx.translate(x, y);
        this.ctx.rotate(rotation);

        const lineWidth = size / 8;
        const lineLength = size;
        
        // 八个方向绘制卦象
        for (let i = 0; i < 8; i++) {
            this.ctx.save();
            this.ctx.rotate((i * Math.PI) / 4);
            
            this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.3)';
            this.ctx.lineWidth = lineWidth;
            this.ctx.lineCap = 'round';
            
            // 绘制三条线（简化的卦象）
            for (let j = 0; j < 3; j++) {
                this.ctx.beginPath();
                this.ctx.moveTo(size * 0.6, -lineWidth + j * lineWidth * 0.8);
                this.ctx.lineTo(size * 0.9, -lineWidth + j * lineWidth * 0.8);
                this.ctx.stroke();
            }
            
            this.ctx.restore();
        }

        this.ctx.restore();
    }

    // 动画循环
    animate() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制粒子
        this.particles.forEach(particle => {
            particle.x += particle.speedX;
            particle.y += particle.speedY;

            // 边界反弹
            if (particle.x < 0 || particle.x > this.canvas.width) particle.speedX *= -1;
            if (particle.y < 0 || particle.y > this.canvas.height) particle.speedY *= -1;

            // 绘制粒子
            this.ctx.fillStyle = particle.color + particle.opacity + ')';
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fill();
        });

        // 绘制太极图
        const time = Date.now() * 0.001;
        this.drawTaiChi(
            this.canvas.width * 0.2, 
            this.canvas.height * 0.3, 
            30, 
            time * 0.5
        );

        this.drawTaiChi(
            this.canvas.width * 0.8, 
            this.canvas.height * 0.7, 
            25, 
            -time * 0.3
        );

        // 绘制八卦
        this.drawBagua(
            this.canvas.width * 0.5, 
            this.canvas.height * 0.5, 
            40, 
            time * 0.2
        );

        this.animationId = requestAnimationFrame(() => this.animate());
    }

    // 初始化表单
    initForm() {
        this.populateYearOptions();
        this.populateDayOptions();
        this.bindFormEvents();
    }

    // 填充年份选项
    populateYearOptions() {
        const yearSelect = document.getElementById('birthYear');
        if (!yearSelect) return;

        const currentYear = new Date().getFullYear();
        const startYear = currentYear - 100;

        for (let year = currentYear; year >= startYear; year--) {
            const option = document.createElement('option');
            option.value = year;
            option.textContent = `${year}年`;
            yearSelect.appendChild(option);
        }
    }

    // 填充日期选项
    populateDayOptions() {
        const daySelect = document.getElementById('birthDay');
        if (!daySelect) return;

        for (let day = 1; day <= 31; day++) {
            const option = document.createElement('option');
            option.value = day;
            option.textContent = `${day}日`;
            daySelect.appendChild(option);
        }
    }

    // 绑定表单事件
    bindFormEvents() {
        const monthSelect = document.getElementById('birthMonth');
        const daySelect = document.getElementById('birthDay');
        const yearSelect = document.getElementById('birthYear');

        // 月份改变时更新日期选项
        if (monthSelect && daySelect && yearSelect) {
            const updateDays = () => {
                const year = parseInt(yearSelect.value);
                const month = parseInt(monthSelect.value);
                
                if (year && month) {
                    const daysInMonth = new Date(year, month, 0).getDate();
                    const currentDay = daySelect.value;
                    
                    // 清空现有选项
                    daySelect.innerHTML = '<option value="">请选择日期</option>';
                    
                    // 添加正确的日期数
                    for (let day = 1; day <= daysInMonth; day++) {
                        const option = document.createElement('option');
                        option.value = day;
                        option.textContent = `${day}日`;
                        if (day == currentDay && day <= daysInMonth) {
                            option.selected = true;
                        }
                        daySelect.appendChild(option);
                    }
                }
            };

            monthSelect.addEventListener('change', updateDays);
            yearSelect.addEventListener('change', updateDays);
        }

        // 表单提交事件
        const form = document.getElementById('baziForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmit();
            });
        }
    }

    // 处理表单提交
    async handleFormSubmit() {
        const formData = new FormData(document.getElementById('baziForm'));
        const data = {
            name: formData.get('userName'),
            gender: formData.get('gender'),
            year: formData.get('birthYear'),
            month: formData.get('birthMonth'),
            day: formData.get('birthDay'),
            hour: formData.get('birthHour')
        };

        // 验证数据
        if (!data.name || !data.gender || !data.year || !data.month || !data.day || !data.hour) {
            alert('请填写完整的生辰信息');
            return;
        }

        console.log('八字精批数据:', data);
        
        // 创建订单并支付
        try {
            const serviceConfig = {
                type: 'bazi',
                name: '八字精批',
                price: 19.9
            };
            
            // 使用订单支付管理器
            if (window.orderPaymentManager) {
                await window.orderPaymentManager.createOrderAndPay(
                    serviceConfig,
                    data,
                    (order, result) => {
                        console.log('支付成功:', order, result);
                        alert(`感谢${data.name}的信任！\n您的八字精批订单已支付成功，专业大师正在为您精心解读...\n\n预计3-5分钟内完成分析报告。`);
                        this.closeModal();
                    },
                    () => {
                        console.log('支付取消');
                    }
                );
            } else {
                // 降级处理
                alert(`感谢${data.name}的信任！\n您的八字信息已提交，专业大师正在为您精心解读...\n\n预计3-5分钟内完成分析报告。`);
                this.closeModal();
            }
        } catch (error) {
            console.error('订单创建失败:', error);
            alert('订单创建失败，请稍后重试');
        }
    }

    // 绑定事件
    bindEvents() {
        // 绑定服务项点击事件
        document.addEventListener('click', (e) => {
            const serviceItem = e.target.closest('[data-service="bazi"]');
            if (serviceItem) {
                console.log('✨ 八字精批按钮被点击了！跳转到独立页面...');
                // 跳转到独立页面而不是打开模态框
                window.location.href = 'pages/bazi/index.html';
            }
        });

        // 点击遮罩关闭模态框
        if (this.modal) {
            this.modal.addEventListener('click', (e) => {
                if (e.target === this.modal) {
                    this.closeModal();
                }
            });
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('show')) {
                this.closeModal();
            }
        });
    }

    // 打开模态框
    openModal() {
        if (this.modal) {
            this.modal.classList.add('show');
            this.modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            
            // 重新初始化Canvas
            setTimeout(() => {
                this.initCanvas();
                this.createParticles();
            }, 100);
        }
    }

    // 关闭模态框
    closeModal() {
        if (this.modal) {
            this.modal.classList.remove('show');
            setTimeout(() => {
                this.modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }, 300);
        }
    }

    // 销毁动画
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
    }
}

// 全局函数供HTML调用
function closeBaziModal() {
    if (window.baziModule) {
        window.baziModule.closeModal();
    }
}

// 初始化八字模块
document.addEventListener('DOMContentLoaded', () => {
    window.baziModule = new BaziModule();
    
    // 初始化订单支付管理器
    if (typeof OrderPaymentManager !== 'undefined') {
        window.orderPaymentManager = new OrderPaymentManager();
        console.log('✅ 订单支付管理器已初始化');
    } else {
        console.log('❌ 订单支付管理器未加载');
    }
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (window.baziModule) {
        window.baziModule.destroy();
    }
}); 