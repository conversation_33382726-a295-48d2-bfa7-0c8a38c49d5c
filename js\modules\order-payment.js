/**
 * 订单支付模块
 * 处理AI模块的订单创建和支付流程
 */

// 确保使用真实的OrderAPI（来自api-order.js）
if (typeof window.OrderAPI === 'undefined') {
    console.error('OrderAPI未找到，请确保先加载 api-order.js');
    // 提供降级方案
    window.OrderAPI = {
        createOrder: async (orderData) => {
            throw new Error('OrderAPI未加载，请检查页面引入顺序');
        },
        payOrder: async (orderId) => {
            throw new Error('OrderAPI未加载，请检查页面引入顺序');
        }
    };
}

class OrderPaymentManager {
    constructor() {
        this.currentOrder = null;
        this.paymentModal = null;
        this.paymentCompleted = false; // 支付完成标志
        this.onSuccess = null; // 支付成功回调
        this.onCancel = null; // 取消支付回调
        this.init();
    }

    init() {
        // 初始化支付模块
        console.log('订单支付模块已初始化，使用真实API');
    }

    /**
     * 检查会员登录状态
     * @returns {boolean} 是否已登录
     */
    checkMemberLogin() {
        console.log('🔍 开始检查会员登录状态...');
        
        let loginStatus = {
            unifiedAuth: false,
            memberService: false,
            finalResult: false
        };
        
        // 优先检查统一认证服务
        if (window.unifiedAuthService) {
            const isLoggedIn = window.unifiedAuthService.isLoggedIn();
            const userType = window.unifiedAuthService.getUserType();
            const currentUser = window.unifiedAuthService.getCurrentUser();
            
            console.log('统一认证服务状态:', {
                isLoggedIn,
                userType,
                hasCurrentUser: !!currentUser,
                currentUser: currentUser
            });
            
            if (isLoggedIn && userType === 'member') {
                loginStatus.unifiedAuth = true;
                loginStatus.finalResult = true;
                console.log('✅ 统一认证服务检测到会员已登录');
            }
        } else {
            console.warn('⚠️ 统一认证服务未找到');
        }
        
        // 降级检查会员服务
        if (!loginStatus.finalResult && window.memberService) {
            if (window.memberService.isLoggedIn) {
                const isLoggedIn = window.memberService.isLoggedIn();
                const memberInfo = window.memberService.getMemberInfo();
                
                console.log('会员服务状态:', {
                    isLoggedIn,
                    hasMemberInfo: !!memberInfo,
                    memberInfo: memberInfo
                });
                
                if (isLoggedIn) {
                    loginStatus.memberService = true;
                    loginStatus.finalResult = true;
                    console.log('✅ 会员服务检测到已登录');
                }
            } else {
                console.warn('⚠️ 会员服务isLoggedIn方法未找到');
            }
        } else if (!loginStatus.finalResult) {
            console.warn('⚠️ 会员服务未找到');
        }
        
        console.log('📊 最终登录状态:', loginStatus);
        
        if (!loginStatus.finalResult) {
            console.log('❌ 未检测到会员登录状态');
        }
        
        return loginStatus.finalResult;
    }

    /**
     * 显示登录提示
     */
    showLoginPrompt() {
        const modalHTML = `
            <div id="loginPromptModal" class="login-prompt-overlay">
                <div class="login-prompt-modal">
                    <div class="login-prompt-header">
                        <h3>需要登录</h3>
                        <button class="close-btn" onclick="orderPaymentManager.closeLoginPrompt()">×</button>
                    </div>
                    
                    <div class="login-prompt-content">
                        <div class="login-prompt-icon">🔐</div>
                        <h4>请先登录会员账号</h4>
                        <p>为了保障您的权益，使用AI服务前需要先登录会员账号</p>
                        
                        <div class="login-prompt-actions">
                            <button class="login-btn" onclick="orderPaymentManager.goToLogin()">
                                立即登录
                            </button>
                            <button class="register-btn" onclick="orderPaymentManager.goToRegister()">
                                注册会员
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.addLoginPromptStyles();
    }

    /**
     * 关闭登录提示
     */
    closeLoginPrompt() {
        const modal = document.getElementById('loginPromptModal');
        if (modal) {
            modal.remove();
        }
    }

    /**
     * 跳转到登录页面
     */
    goToLogin() {
        this.closeLoginPrompt();
        // 跳转到登录页面或显示登录弹窗
        if (window.showLoginModal) {
            window.showLoginModal();
        } else {
            window.location.href = '/pages/login/';
        }
    }

    /**
     * 跳转到注册页面
     */
    goToRegister() {
        this.closeLoginPrompt();
        // 跳转到注册页面或显示注册弹窗
        if (window.showRegisterModal) {
            window.showRegisterModal();
        } else {
            window.location.href = '/pages/register/';
        }
    }

    /**
     * 创建订单并显示支付弹窗
     * @param {Object} serviceConfig - 服务配置
     * @param {Object} userData - 用户数据
     * @param {Function} onSuccess - 支付成功回调
     * @param {Function} onCancel - 取消支付回调
     */
    async createOrderAndPay(serviceConfig, userData, onSuccess = null, onCancel = null) {
        // 检查会员登录状态
        if (!this.checkMemberLogin()) {
            this.showLoginPrompt();
            return;
        }
        try {
            // 显示加载状态
            this.showLoading('正在创建订单...');

            // 获取会员信息 - 优先从统一认证服务获取
            let memberInfo = null;
            
            if (window.unifiedAuthService && window.unifiedAuthService.isLoggedIn()) {
                const userType = window.unifiedAuthService.getUserType();
                if (userType === 'member') {
                    memberInfo = window.unifiedAuthService.getCurrentUser();
                    console.log('从统一认证服务获取会员信息:', memberInfo);
                }
            }
            
            // 降级到会员服务
            if (!memberInfo && window.memberService) {
                memberInfo = window.memberService.getMemberInfo();
                console.log('从会员服务获取会员信息:', memberInfo);
            }
            
            if (!memberInfo) {
                throw new Error('无法获取会员信息');
            }
            
            // 准备订单数据 - 后端现在从Sa-Token获取用户ID
            const orderData = {
                serviceType: serviceConfig.type,
                serviceName: serviceConfig.name,
                amount: serviceConfig.price,
                userData: JSON.stringify(userData), // 用户数据转为JSON字符串
                // 不再传递memberId，后端从Sa-Token获取
                memberName: memberInfo.userName // 保留会员用户名用于显示
            };

            console.log('创建订单数据:', orderData);

            // 调用真实API创建订单
            const order = await window.OrderAPI.createOrder(orderData);
            this.currentOrder = order;

            console.log('订单创建成功:', order);

            // 隐藏加载状态
            this.hideLoading();

            // 显示支付弹窗
            this.showPaymentModal(order, onSuccess, onCancel);

        } catch (error) {
            console.error('创建订单失败:', error);
            this.hideLoading();
            this.showError('创建订单失败: ' + (error.message || '请稍后重试'));
        }
    }

    /**
     * 显示支付弹窗
     */
    showPaymentModal(order, onSuccess = null, onCancel = null) {
        // 创建支付弹窗HTML
        const modalHTML = `
            <div id="paymentModal" class="payment-modal-overlay">
                <div class="payment-modal">
                    <div class="payment-header">
                        <h3>订单支付</h3>
                        <button class="close-btn" onclick="orderPaymentManager.closePaymentModal()">×</button>
                    </div>
                    
                    <div class="payment-content">
                        <div class="order-info">
                            <div class="order-item">
                                <span class="label">服务项目：</span>
                                <span class="value">${order.serviceName}</span>
                            </div>
                            <div class="order-item">
                                <span class="label">订单金额：</span>
                                <span class="value price">¥${order.amount}</span>
                            </div>
                            <div class="order-item">
                                <span class="label">订单编号：</span>
                                <span class="value">${order.orderNumber || order.id}</span>
                            </div>
                            <div class="order-item">
                                <span class="label">订单状态：</span>
                                <span class="value status">${this.getStatusText(order.status)}</span>
                            </div>
                            <div class="order-item">
                                <span class="label">会员账号：</span>
                                <span class="value">${order.memberName || '未知'}</span>
                            </div>
                        </div>
                        
                        <div class="payment-methods">
                            <h4>选择支付方式</h4>
                            <div class="payment-options">
                                <label class="payment-option">
                                    <input type="radio" name="paymentMethod" value="wechat" checked>
                                    <span class="payment-icon">💚</span>
                                    <span>微信支付</span>
                                </label>
                                <label class="payment-option">
                                    <input type="radio" name="paymentMethod" value="alipay">
                                    <span class="payment-icon">💙</span>
                                    <span>支付宝</span>
                                </label>
                                <label class="payment-option">
                                    <input type="radio" name="paymentMethod" value="card">
                                    <span class="payment-icon">💳</span>
                                    <span>银行卡</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="payment-actions">
                        <button class="pay-btn" onclick="orderPaymentManager.processPayment()">
                            立即支付 ¥${order.amount}
                        </button>
                        <button class="cancel-btn" onclick="orderPaymentManager.closePaymentModal()">
                            取消支付
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.paymentModal = document.getElementById('paymentModal');

        // 保存回调函数
        this.onSuccess = onSuccess;
        this.onCancel = onCancel;

        // 添加样式
        this.addPaymentStyles();
    }

    /**
     * 处理支付
     */
    async processPayment() {
        if (!this.currentOrder) {
            this.showError('订单信息错误');
            return;
        }

        const payBtn = this.paymentModal.querySelector('.pay-btn');
        const originalText = payBtn.textContent;
        payBtn.textContent = '支付中...';
        payBtn.disabled = true;

        try {
            // 获取选择的支付方式
            const paymentMethod = this.paymentModal.querySelector('input[name="paymentMethod"]:checked').value;
            
            console.log('💰 开始支付流程...');
            console.log('💳 支付方式:', paymentMethod);
            console.log('📋 订单信息:', this.currentOrder);
            
            // 模拟支付过程
            await this.simulatePayment(paymentMethod);
            
            // 调用真实后端支付API
            const result = await window.OrderAPI.payOrder(this.currentOrder.id);
            
            console.log('✅ 支付成功:', result);
            
            // 设置支付完成标志
            this.paymentCompleted = true;
            
            // 支付成功
            this.showPaymentSuccess(result);
            
            // 调用成功回调
            if (this.onSuccess) {
                console.log('🎯 调用支付成功回调...');
                this.onSuccess(this.currentOrder, result);
            } else {
                console.warn('⚠️ 没有设置支付成功回调');
            }

        } catch (error) {
            console.error('❌ 支付失败:', error);
            this.showPaymentFailed(error.message || '支付失败，请重试');
        } finally {
            payBtn.textContent = originalText;
            payBtn.disabled = false;
        }
    }

    /**
     * 模拟支付过程
     */
    async simulatePayment(paymentMethod) {
        return new Promise((resolve) => {
            // 显示支付进度
            this.showPaymentProgress(paymentMethod);
            
            // 模拟2秒支付时间
            setTimeout(() => {
                resolve();
            }, 2000);
        });
    }

    /**
     * 显示支付进度
     */
    showPaymentProgress(paymentMethod) {
        const paymentContent = this.paymentModal.querySelector('.payment-content');
        const progressHTML = `
            <div class="payment-progress">
                <div class="progress-spinner"></div>
                <p>正在连接${this.getPaymentMethodName(paymentMethod)}...</p>
                <p class="progress-text">请稍候，正在处理支付请求</p>
            </div>
        `;
        
        paymentContent.innerHTML = progressHTML;
    }

    /**
     * 获取支付方式名称
     */
    getPaymentMethodName(method) {
        const methodNames = {
            'wechat': '微信支付',
            'alipay': '支付宝',
            'card': '银行卡'
        };
        return methodNames[method] || method;
    }

    /**
     * 显示支付成功
     */
    showPaymentSuccess(result) {
        const paymentContent = this.paymentModal.querySelector('.payment-content');
        const successHTML = `
            <div class="payment-success">
                <div class="success-icon">✅</div>
                <h3>支付成功！</h3>
                <p>订单已支付完成，正在为您准备服务...</p>
                <div class="success-details">
                    <p><strong>订单号：</strong>${this.currentOrder.orderNumber || this.currentOrder.id}</p>
                    <p><strong>支付金额：</strong>¥${this.currentOrder.amount}</p>
                    <p><strong>支付时间：</strong>${new Date().toLocaleString()}</p>
                </div>
            </div>
        `;
        
        paymentContent.innerHTML = successHTML;
        
        // 3秒后自动关闭
        setTimeout(() => {
            this.closePaymentModal();
        }, 3000);
    }

    /**
     * 显示支付失败
     */
    showPaymentFailed(errorMessage) {
        const paymentContent = this.paymentModal.querySelector('.payment-content');
        const failedHTML = `
            <div class="payment-failed">
                <div class="failed-icon">❌</div>
                <h3>支付失败</h3>
                <p>${errorMessage}</p>
                <button class="retry-btn" onclick="orderPaymentManager.processPayment()">
                    重新支付
                </button>
            </div>
        `;
        
        paymentContent.innerHTML = failedHTML;
    }

    /**
     * 关闭支付弹窗
     */
    closePaymentModal() {
        if (this.paymentModal) {
            this.paymentModal.remove();
            this.paymentModal = null;
        }
        
        // 只有在用户主动取消时才调用取消回调
        // 支付成功时会自动关闭，不调用取消回调
        if (this.onCancel && !this.paymentCompleted) {
            this.onCancel(this.currentOrder);
        }
        
        // 重置支付状态
        this.paymentCompleted = false;
    }

    /**
     * 显示加载状态
     */
    showLoading(message = '处理中...') {
        // 创建加载弹窗
        const loadingHTML = `
            <div id="loadingModal" class="loading-overlay">
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <p>${message}</p>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', loadingHTML);
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loadingModal = document.getElementById('loadingModal');
        if (loadingModal) {
            loadingModal.remove();
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        alert('错误: ' + message);
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'PENDING': '待支付',
            'PAID': '已支付',
            'CANCELLED': '已取消',
            'COMPLETED': '已完成'
        };
        return statusMap[status] || status;
    }

    /**
     * 添加支付弹窗样式
     */
    addPaymentStyles() {
        const styleId = 'payment-modal-styles';
        if (document.getElementById(styleId)) return;

        const styles = `
            .payment-modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            }
            
            .payment-modal {
                background: white;
                border-radius: 12px;
                padding: 0;
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            }
            
            .payment-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px;
                border-bottom: 1px solid #eee;
            }
            
            .payment-header h3 {
                margin: 0;
                color: #333;
            }
            
            .close-btn {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #999;
            }
            
            .payment-content {
                padding: 20px;
            }
            
            .order-info {
                margin-bottom: 20px;
            }
            
            .order-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
                padding: 8px 0;
                border-bottom: 1px solid #f5f5f5;
            }
            
            .order-item .label {
                color: #666;
                font-weight: 500;
            }
            
            .order-item .value {
                color: #333;
                font-weight: 600;
            }
            
            .order-item .price {
                color: #e74c3c;
                font-size: 18px;
            }
            
            .payment-methods h4 {
                margin-bottom: 15px;
                color: #333;
            }
            
            .payment-options {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }
            
            .payment-option {
                display: flex;
                align-items: center;
                padding: 15px;
                border: 2px solid #eee;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s;
            }
            
            .payment-option:hover {
                border-color: #3498db;
                background: #f8f9ff;
            }
            
            .payment-option input[type="radio"] {
                margin-right: 10px;
            }
            
            .payment-icon {
                margin-right: 10px;
                font-size: 20px;
            }
            
            .payment-actions {
                padding: 20px;
                border-top: 1px solid #eee;
                display: flex;
                gap: 10px;
            }
            
            .pay-btn {
                flex: 1;
                padding: 12px 20px;
                background: linear-gradient(135deg, #e74c3c, #c0392b);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s;
            }
            
            .pay-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
            }
            
            .cancel-btn {
                padding: 12px 20px;
                background: #f8f9fa;
                color: #666;
                border: 1px solid #ddd;
                border-radius: 8px;
                font-size: 16px;
                cursor: pointer;
                transition: all 0.3s;
            }
            
            .cancel-btn:hover {
                background: #e9ecef;
            }
            
            .payment-progress {
                text-align: center;
                padding: 40px 20px;
            }
            
            .progress-spinner {
                width: 50px;
                height: 50px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #3498db;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 20px;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .payment-success, .payment-failed {
                text-align: center;
                padding: 40px 20px;
            }
            
            .success-icon, .failed-icon {
                font-size: 60px;
                margin-bottom: 20px;
            }
            
            .success-details {
                margin-top: 20px;
                text-align: left;
                background: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
            }
            
            .retry-btn {
                margin-top: 20px;
                padding: 10px 20px;
                background: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                cursor: pointer;
            }
            
            .loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10001;
            }
            
            .loading-content {
                background: white;
                padding: 30px;
                border-radius: 12px;
                text-align: center;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            }
            
            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #3498db;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 15px;
            }
        `;

        const styleElement = document.createElement('style');
        styleElement.id = styleId;
        styleElement.textContent = styles;
        document.head.appendChild(styleElement);
    }

    /**
     * 添加登录提示样式
     */
    addLoginPromptStyles() {
        const styleId = 'login-prompt-styles';
        if (document.getElementById(styleId)) return;

        const styles = `
            .login-prompt-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            }
            
            .login-prompt-modal {
                background: white;
                border-radius: 12px;
                padding: 0;
                max-width: 400px;
                width: 90%;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            }
            
            .login-prompt-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px;
                border-bottom: 1px solid #eee;
            }
            
            .login-prompt-header h3 {
                margin: 0;
                color: #333;
            }
            
            .login-prompt-content {
                padding: 30px 20px;
                text-align: center;
            }
            
            .login-prompt-icon {
                font-size: 60px;
                margin-bottom: 20px;
            }
            
            .login-prompt-content h4 {
                margin: 0 0 10px 0;
                color: #333;
            }
            
            .login-prompt-content p {
                color: #666;
                margin-bottom: 30px;
            }
            
            .login-prompt-actions {
                display: flex;
                gap: 10px;
            }
            
            .login-btn, .register-btn {
                flex: 1;
                padding: 12px 20px;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s;
            }
            
            .login-btn {
                background: linear-gradient(135deg, #3498db, #2980b9);
                color: white;
            }
            
            .register-btn {
                background: linear-gradient(135deg, #e74c3c, #c0392b);
                color: white;
            }
            
            .login-btn:hover, .register-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            }
        `;

        const styleElement = document.createElement('style');
        styleElement.id = styleId;
        styleElement.textContent = styles;
        document.head.appendChild(styleElement);
    }
}

// 创建全局订单支付管理器实例
window.orderPaymentManager = new OrderPaymentManager(); 