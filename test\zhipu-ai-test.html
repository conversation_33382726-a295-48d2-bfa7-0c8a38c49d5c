<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智谱AI服务测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .config-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .image-result {
            margin-top: 10px;
        }
        .image-result img {
            max-width: 200px;
            margin: 5px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 智谱AI服务测试</h1>
        
        <div class="config-section">
            <h3>📋 配置信息</h3>
            <label>API密钥:</label>
            <input type="text" id="apiKey" placeholder="请输入您的智谱AI API密钥">
            <label>文本模型:</label>
            <input type="text" id="textModel" value="glm-4-flash">
            <label>图像模型:</label>
            <input type="text" id="imageModel" value="cogview-3-plus">
            <button onclick="updateConfig()">更新配置</button>
        </div>

        <div class="test-section">
            <h3>🔍 服务状态检查</h3>
            <button onclick="checkServices()">检查服务状态</button>
            <div id="serviceStatus" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📝 文本生成测试</h3>
            <button onclick="testTextGeneration()">测试文本生成</button>
            <div id="textResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🎨 图像生成测试</h3>
            <button onclick="testImageGeneration()">测试图像生成</button>
            <div id="imageResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>💕 完整姻缘画像测试</h3>
            <button onclick="testFullPortrait()">测试完整流程</button>
            <div id="portraitResult" class="result"></div>
        </div>
    </div>

    <!-- 引入智谱AI服务 -->
    <script src="../js/modules/zhipu-ai-service.js"></script>
    
    <script>
        let zhipuService = null;

        // 更新配置
        function updateConfig() {
            const apiKey = document.getElementById('apiKey').value;
            const textModel = document.getElementById('textModel').value;
            const imageModel = document.getElementById('imageModel').value;

            if (!apiKey) {
                showResult('serviceStatus', '请先输入API密钥', 'error');
                return;
            }

            try {
                zhipuService = new ZhipuAIService({
                    ZHIPU_API_KEY: apiKey,
                    ZHIPU_TEXT_MODEL: textModel,
                    ZHIPU_IMAGE_MODEL: imageModel,
                    SERVICE_TYPE: 'zhipu'
                });
                showResult('serviceStatus', '✅ 智谱AI服务配置成功', 'success');
            } catch (error) {
                showResult('serviceStatus', `❌ 配置失败: ${error.message}`, 'error');
            }
        }

        // 检查服务状态
        function checkServices() {
            const status = {
                'ZhipuAIService类': typeof ZhipuAIService !== 'undefined',
                '服务实例': zhipuService !== null,
                'API密钥': zhipuService && zhipuService.config.zhipu.apiKey.length > 0
            };

            let result = '服务状态检查:\n';
            for (const [key, value] of Object.entries(status)) {
                result += `${value ? '✅' : '❌'} ${key}: ${value ? '正常' : '异常'}\n`;
            }

            showResult('serviceStatus', result, Object.values(status).every(v => v) ? 'success' : 'error');
        }

        // 测试文本生成
        async function testTextGeneration() {
            if (!zhipuService) {
                showResult('textResult', '请先配置智谱AI服务', 'error');
                return;
            }

            showResult('textResult', '正在生成文本描述...', 'loading');

            const testData = {
                gender: '女',
                age: '25-30岁',
                zodiac: '天秤座',
                priorities: ['温柔', '知性'],
                appearance: {
                    age: '28',
                    height: '165',
                    bodyType: '苗条',
                    style: '优雅'
                },
                personality: {
                    primary: ['温柔', '知性'],
                    communication: '温和'
                }
            };

            try {
                const description = await zhipuService.generatePortraitDescription(testData);
                showResult('textResult', `✅ 文本生成成功:\n\n${description}`, 'success');
            } catch (error) {
                showResult('textResult', `❌ 文本生成失败: ${error.message}`, 'error');
            }
        }

        // 测试图像生成
        async function testImageGeneration() {
            if (!zhipuService) {
                showResult('imageResult', '请先配置智谱AI服务', 'error');
                return;
            }

            showResult('imageResult', '正在生成图像...', 'loading');

            const testDescription = '一位优雅知性的女性，温柔的笑容，苗条的身材，穿着优雅的服装';

            try {
                const imageUrl = await zhipuService.generatePortraitImage(testDescription);
                const resultDiv = document.getElementById('imageResult');
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ 图像生成成功:<div class="image-result"><img src="${imageUrl}" alt="生成的画像"></div>`;
            } catch (error) {
                showResult('imageResult', `❌ 图像生成失败: ${error.message}`, 'error');
            }
        }

        // 测试完整姻缘画像流程
        async function testFullPortrait() {
            if (!zhipuService) {
                showResult('portraitResult', '请先配置智谱AI服务', 'error');
                return;
            }

            showResult('portraitResult', '正在生成完整姻缘画像...', 'loading');

            const testData = {
                gender: '女',
                age: '25-30岁',
                zodiac: '天秤座',
                priorities: ['温柔', '知性', '优雅'],
                appearance: {
                    age: '28',
                    height: '165',
                    bodyType: '苗条',
                    style: '优雅'
                },
                personality: {
                    primary: ['温柔', '知性'],
                    communication: '温和'
                },
                lifestyle: {
                    activities: ['读书', '音乐'],
                    livingStyle: '有条理'
                }
            };

            try {
                const images = await zhipuService.generatePortraitImages(testData);
                const resultDiv = document.getElementById('portraitResult');
                resultDiv.className = 'result success';
                
                let html = `✅ 完整画像生成成功，共生成 ${images.length} 张图片:<div class="image-result">`;
                images.forEach((imageUrl, index) => {
                    html += `<img src="${imageUrl}" alt="画像${index + 1}">`;
                });
                html += '</div>';
                
                resultDiv.innerHTML = html;
            } catch (error) {
                showResult('portraitResult', `❌ 完整画像生成失败: ${error.message}`, 'error');
            }
        }

        // 显示结果
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // 页面加载时的初始化
        window.onload = function() {
            checkServices();
        };
    </script>
</body>
</html>
