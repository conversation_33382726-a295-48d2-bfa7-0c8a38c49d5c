/* 轮播图组件样式 */
.banner-carousel {
    margin: 0 15px 20px;
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    position: relative;
}

.carousel-container {
    position: relative;
    height: 150px;
    overflow: hidden;
}

.carousel-item {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease;
    transform: translateX(100%);
}

.carousel-item.active {
    opacity: 1;
    transform: translateX(0);
}

.carousel-item.prev {
    transform: translateX(-100%);
}

.carousel-item.next {
    transform: translateX(100%);
}

/* 轮播内容样式 */
.banner-content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    position: relative;
    overflow: hidden;
    background-size: cover;
    background-position: center;
}

.banner-content.name-banner {
    background: linear-gradient(135deg, #FFE4B5, #FFDEAD);
    position: relative;
}

.banner-content.name-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="rgba(139,69,19,0.1)" stroke-width="2"/></svg>');
    background-size: 50px 50px;
    animation: rotate 20s linear infinite;
}

.banner-text {
    position: relative;
    z-index: 2;
}

.banner-text h2 {
    font-size: 24px;
    color: #8B4513;
    font-weight: 700;
    margin-bottom: 5px;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.5);
    animation: slideInLeft 0.8s ease-out;
}

.banner-text p {
    font-size: 16px;
    color: #A0522D;
    font-weight: 500;
    animation: slideInLeft 0.8s ease-out 0.2s both;
}

.banner-decoration {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    opacity: 0.7;
    animation: float 3s ease-in-out infinite;
}

.banner-decoration img {
    width: 80px;
    height: 80px;
    object-fit: contain;
}

/* 圆点指示器 */
.carousel-dots {
    display: flex;
    justify-content: center;
    gap: 8px;
    padding: 10px;
    background: var(--bg-light);
    position: relative;
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #DDD;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.dot::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-pink);
    transition: left 0.3s ease;
}

.dot.active::before {
    left: 0;
}

.dot.active {
    background: var(--primary-pink);
    transform: scale(1.2);
}

.dot:hover {
    transform: scale(1.1);
    background: rgba(255, 105, 180, 0.6);
}

/* 轮播控制箭头 */
.carousel-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.8);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: var(--text-dark);
    opacity: 0;
    transition: var(--transition);
    z-index: 10;
}

.banner-carousel:hover .carousel-arrow {
    opacity: 1;
}

.carousel-arrow.prev {
    left: 10px;
}

.carousel-arrow.next {
    right: 10px;
}

.carousel-arrow:hover {
    background: rgba(255,255,255,0.95);
    transform: translateY(-50%) scale(1.1);
}

/* 进度条 */
.carousel-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: var(--primary-pink);
    transition: width 3s linear;
    width: 0;
}

.carousel-item.active .carousel-progress {
    width: 100%;
}

/* 动画效果 */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(-50%) translateX(0);
    }
    50% {
        transform: translateY(-50%) translateX(5px);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 触摸滑动指示 */
.carousel-container::after {
    content: '← 滑动查看更多 →';
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 10px;
    color: rgba(139,69,19,0.6);
    pointer-events: none;
    opacity: 0;
    animation: fadeInOut 3s ease-in-out infinite;
}

@keyframes fadeInOut {
    0%, 100% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
}

/* 响应式适配 */
@media (max-width: 480px) {
    .banner-carousel {
        margin: 0 10px 15px;
    }
    
    .carousel-container {
        height: 120px;
    }
    
    .banner-content {
        padding: 15px;
    }
    
    .banner-text h2 {
        font-size: 20px;
    }
    
    .banner-text p {
        font-size: 14px;
    }
    
    .banner-decoration img {
        width: 60px;
        height: 60px;
    }
    
    .carousel-arrow {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }
}

@media (max-width: 360px) {
    .carousel-container {
        height: 100px;
    }
    
    .banner-text h2 {
        font-size: 18px;
    }
    
    .banner-text p {
        font-size: 12px;
    }
} 