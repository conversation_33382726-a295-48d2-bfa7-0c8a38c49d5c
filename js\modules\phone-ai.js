/**
 * 手机号AI分析模块
 * 集成DeepSeek API进行深度手机号吉凶分析
 */

class PhoneNumberAI {
    constructor() {
        this.isAnalyzing = false;
        this.lastAnalysis = null;
        this.initializeKnowledgeBase();
        // 初始化认证服务
        if (window.initializeAuthServices) {
            window.initializeAuthServices();
        }
    }

    // 初始化知识库
    initializeKnowledgeBase() {
        this.knowledgeBase = {
            // 数字能量学基础
            magneticFields: {
                shengqi: {
                    name: '生气磁场',
                    numbers: ['14', '41', '23', '32', '68', '86', '77', '95', '59'],
                    energy: '积极向上',
                    effects: ['提升活力', '增强创新能力', '促进事业发展', '改善人际关系'],
                    score: 90
                },
                tianyi: {
                    name: '天医磁场',
                    numbers: ['13', '31', '24', '42', '69', '96', '78', '87'],
                    energy: '财富健康',
                    effects: ['增强财运', '促进健康', '带来贵人', '事业顺利'],
                    score: 95
                },
                yannian: {
                    name: '延年磁场',
                    numbers: ['19', '91', '28', '82', '67', '76', '34', '43'],
                    energy: '贵人助力',
                    effects: ['贵人相助', '人际和谐', '事业稳定', '延年益寿'],
                    score: 85
                },
                fuwei: {
                    name: '伏位磁场',
                    numbers: ['11', '22', '33', '44', '55', '66', '77', '88', '99'],
                    energy: '稳定平和',
                    effects: ['保持稳定', '减少变动', '平稳发展', '安分守己'],
                    score: 70
                },
                jueming: {
                    name: '绝命磁场',
                    numbers: ['12', '21', '39', '93', '48', '84', '67', '76'],
                    energy: '破坏极强',
                    effects: ['财运不佳', '健康问题', '人际冲突', '事业阻碍'],
                    score: 30
                },
                wugui: {
                    name: '五鬼磁场',
                    numbers: ['18', '81', '29', '92', '37', '73', '46', '64'],
                    energy: '意外频发',
                    effects: ['意外事件', '小人是非', '财务损失', '情绪波动'],
                    score: 40
                },
                liusha: {
                    name: '六煞磁场',
                    numbers: ['16', '61', '27', '72', '38', '83', '49', '94'],
                    energy: '感情波折',
                    effects: ['感情波折', '桃花劫', '人际问题', '情绪不稳'],
                    score: 45
                },
                huohai: {
                    name: '祸害磁场',
                    numbers: ['17', '71', '26', '62', '35', '53', '48', '84'],
                    energy: '口舌是非',
                    effects: ['口舌是非', '官司纠纷', '人际矛盾', '工作不顺'],
                    score: 50
                }
            },

            // 数字含义库
            numberMeanings: {
                '0': { meaning: '圆满', energy: '中性', description: '代表无限可能、圆满完整、包容万物' },
                '1': { meaning: '独立', energy: '阳性', description: '代表开始、领导、独立、自主、创新精神' },
                '2': { meaning: '合作', energy: '阴性', description: '代表合作、平衡、温和、协调、人际和谐' },
                '3': { meaning: '创造', energy: '阳性', description: '代表创造、表达、沟通、才华、艺术天赋' },
                '4': { meaning: '稳定', energy: '阴性', description: '代表稳定、务实、勤劳、组织、系统性思维' },
                '5': { meaning: '自由', energy: '阳性', description: '代表自由、变化、冒险、多元、灵活适应' },
                '6': { meaning: '和谐', energy: '阴性', description: '代表和谐、责任、关爱、家庭、服务他人' },
                '7': { meaning: '智慧', energy: '阳性', description: '代表智慧、直觉、研究、神秘、精神追求' },
                '8': { meaning: '成功', energy: '阳性', description: '代表成功、权力、物质、财富、商业头脑' },
                '9': { meaning: '完成', energy: '阳性', description: '代表完成、奉献、智慧、普世、大爱精神' }
            },

            // 运营商能量分析
            carrierEnergy: {
                '中国移动': { energy: '火性', traits: ['积极进取', '发展迅速', '创新变革'], score: 85 },
                '中国联通': { energy: '风性', traits: ['灵活多变', '沟通顺畅', '人际广阔'], score: 80 },
                '中国电信': { energy: '土性', traits: ['稳定可靠', '根基扎实', '持续发展'], score: 82 }
            },

            // 号段特殊寓意
            segmentMeanings: {
                '138': '一生发财',
                '139': '一生发久',
                '188': '要发发',
                '168': '一路发',
                '158': '要我发',
                '186': '要发顺',
                '159': '要我久',
                '137': '一生气',
                '135': '一生我',
                '189': '要发久'
            }
        };
    }

    // 生成AI分析提示词
    generateAIPrompt(phoneNumber, basicAnalysis) {
        const prompt = `请对以下手机号码进行深度分析：

手机号码：${phoneNumber}
运营商：${basicAnalysis.carrier}
基础评分：${basicAnalysis.overallScore}分

已检测到的磁场组合：
${basicAnalysis.magneticAnalysis.map(item => 
    `- ${item.combination} (${item.field.name}): ${item.field.description}`
).join('\n')}

能量平衡状况：
- 正能量磁场：${basicAnalysis.energyBalance.positive}个
- 负能量磁场：${basicAnalysis.energyBalance.negative}个
- 中性磁场：${basicAnalysis.energyBalance.neutral}个
- 主导能量：${basicAnalysis.energyBalance.dominant}

请从以下几个维度进行深度分析：

1. **个性特征分析**：根据数字组合分析使用者的性格特点、行为模式、天赋能力

2. **事业财运预测**：分析此号码对事业发展、财富积累、商业机会的影响

3. **人际关系影响**：预测对人际交往、社交能力、贵人运势的作用

4. **健康运势评估**：从数字能量角度分析对身心健康的潜在影响

5. **感情婚姻指导**：评估对恋爱关系、婚姻生活、家庭和谐的作用

6. **专业改运建议**：提供具体的改运方法、使用技巧、注意事项

7. **最佳使用时机**：建议何时使用此号码效果最佳，避免什么时候使用

请以专业且易懂的方式回答，字数控制在800-1000字以内，语言要贴近生活，有实用价值。`;

        return prompt;
    }

    // 调用AI接口进行深度分析
    async generateAIAnalysis(phoneNumber, basicAnalysis) {
        if (!window.aiService) {
            throw new Error('AI服务未初始化，请检查配置');
        }

        console.log('开始AI手机号分析:', phoneNumber);

        try {
            const prompt = this.generateAIPrompt(phoneNumber, basicAnalysis);
            console.log('AI分析提示词:', prompt);

            let aiResponse;
            if (window.AI_CONFIG.SERVICE_TYPE === 'deepseek') {
                const systemPrompt = "你是一位精通数字能量学和手机号码分析的专业大师，能够进行准确的手机号码吉凶分析和改运指导。请用中文回复，严格按照指定的格式输出分析结果。";
                aiResponse = await window.aiService.callDeepSeek(prompt, systemPrompt);
            } else if (window.AI_CONFIG.SERVICE_TYPE === 'dify') {
                aiResponse = await window.aiService.callDify(prompt);
            } else {
                throw new Error('不支持的AI服务类型');
            }

            console.log('AI分析响应:', aiResponse);
            return this.parseAIResponse(aiResponse);

        } catch (error) {
            console.error('AI分析失败:', error);
            throw error;
        }
    }

    // 解析AI响应
    parseAIResponse(response) {
        try {
            // 尝试从响应中提取结构化信息
            const sections = {
                personality: this.extractSection(response, ['个性特征', '性格特点', '个人特质']),
                career: this.extractSection(response, ['事业财运', '财运事业', '事业发展']),
                relationships: this.extractSection(response, ['人际关系', '社交能力', '人缘运势']),
                health: this.extractSection(response, ['健康运势', '身心健康', '健康状况']),
                love: this.extractSection(response, ['感情婚姻', '婚姻感情', '恋爱关系']),
                suggestions: this.extractSection(response, ['改运建议', '使用建议', '专业建议']),
                timing: this.extractSection(response, ['使用时机', '最佳时机', '注意事项'])
            };

            return {
                fullAnalysis: response,
                sections: sections,
                summary: this.generateSummary(response),
                recommendations: this.extractRecommendations(response),
                score: this.extractAIScore(response) || 75,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            console.warn('AI响应解析失败，使用原始文本:', error);
            return {
                fullAnalysis: response,
                sections: { general: response },
                summary: response.substring(0, 200) + '...',
                recommendations: ['建议详细咨询专业人士'],
                score: 75,
                timestamp: new Date().toISOString()
            };
        }
    }

    // 提取特定章节内容
    extractSection(text, keywords) {
        for (const keyword of keywords) {
            const regex = new RegExp(`[*]*\\s*${keyword}[*]*[：:]?([\\s\\S]*?)(?=\\n\\n|\\n[*]*\\s*[一-龥]+[*]*[：:]|$)`, 'i');
            const match = text.match(regex);
            if (match && match[1]) {
                return match[1].trim();
            }
        }
        return '';
    }

    // 生成摘要
    generateSummary(text) {
        const sentences = text.split(/[。！？]/);
        const summary = sentences.slice(0, 3).join('。');
        return summary.length > 150 ? summary.substring(0, 150) + '...' : summary;
    }

    // 提取建议
    extractRecommendations(text) {
        const recommendations = [];
        const lines = text.split('\n');
        
        for (const line of lines) {
            if (line.includes('建议') || line.includes('推荐') || line.includes('应该')) {
                recommendations.push(line.trim());
            }
        }
        
        return recommendations.length > 0 ? recommendations.slice(0, 5) : 
               ['定期清理手机保持正能量', '重要时刻优先使用此号码', '配合个人磁场进行调整'];
    }

    // 提取AI评分
    extractAIScore(text) {
        const scoreMatch = text.match(/(\d{1,2})[分点]/);
        if (scoreMatch) {
            const score = parseInt(scoreMatch[1]);
            return score >= 50 && score <= 100 ? score : null;
        }
        return null;
    }

    // 生成本地分析（AI失败时的降级方案）
    generateLocalAnalysis(phoneNumber, basicAnalysis) {
        const analysis = {
            fullAnalysis: '',
            sections: {},
            summary: '',
            recommendations: [],
            score: basicAnalysis.overallScore,
            timestamp: new Date().toISOString(),
            isLocal: true
        };

        // 生成基础分析
        analysis.sections.personality = this.analyzePersonality(phoneNumber);
        analysis.sections.career = this.analyzeCareer(basicAnalysis);
        analysis.sections.relationships = this.analyzeRelationships(basicAnalysis);
        analysis.sections.suggestions = this.generateLocalSuggestions(basicAnalysis);

        // 生成完整分析文本
        analysis.fullAnalysis = `
📱 手机号码：${phoneNumber}

🎯 综合评分：${analysis.score}分

👤 个性特征：${analysis.sections.personality}

💼 事业财运：${analysis.sections.career}

👥 人际关系：${analysis.sections.relationships}

💡 专业建议：${analysis.sections.suggestions}

本分析基于数字能量学理论，仅供参考。建议结合个人实际情况综合考虑。
        `.trim();

        analysis.summary = `您的手机号码综合评分${analysis.score}分，${this.getScoreDescription(analysis.score)}。`;
        analysis.recommendations = this.generateLocalRecommendations(basicAnalysis);

        return analysis;
    }

    // 分析个性特征
    analyzePersonality(phoneNumber) {
        const traits = [];
        const numbers = phoneNumber.split('');
        
        // 分析主导数字
        const digitCount = {};
        numbers.forEach(digit => {
            digitCount[digit] = (digitCount[digit] || 0) + 1;
        });
        
        const dominantDigit = Object.keys(digitCount).reduce((a, b) => 
            digitCount[a] > digitCount[b] ? a : b
        );
        
        const meaning = this.knowledgeBase.numberMeanings[dominantDigit];
        if (meaning) {
            traits.push(`主导特质：${meaning.meaning}型人格，${meaning.description}`);
        }
        
        // 分析尾号
        const lastDigit = phoneNumber.slice(-1);
        const lastMeaning = this.knowledgeBase.numberMeanings[lastDigit];
        if (lastMeaning) {
            traits.push(`行动模式：${lastMeaning.description}`);
        }
        
        return traits.join('。');
    }

    // 分析事业财运
    analyzeCareer(basicAnalysis) {
        const positiveFields = basicAnalysis.magneticAnalysis.filter(item => 
            item.field.name.includes('天医') || item.field.name.includes('生气')
        );
        
        if (positiveFields.length > 0) {
            return '您的号码包含有利事业发展的磁场，有助于财富积累和事业进步，适合商业活动和投资理财。';
        } else {
            return '建议在事业发展方面保持稳健态度，可通过努力和智慧来改善财运状况。';
        }
    }

    // 分析人际关系
    analyzeRelationships(basicAnalysis) {
        const relationFields = basicAnalysis.magneticAnalysis.filter(item => 
            item.field.name.includes('延年') || item.field.name.includes('伏位')
        );
        
        if (relationFields.length > 0) {
            return '您的号码有利于人际关系发展，容易得到贵人相助，社交能力较强，适合从事需要合作的工作。';
        } else {
            return '在人际交往中需要更多主动性，通过真诚待人来建立良好的人际关系网络。';
        }
    }

    // 生成本地建议
    generateLocalSuggestions(basicAnalysis) {
        const suggestions = [];
        
        if (basicAnalysis.overallScore >= 80) {
            suggestions.push('您的号码能量很好，建议继续使用并在重要场合优先使用');
        } else if (basicAnalysis.overallScore >= 60) {
            suggestions.push('号码能量中等，可通过调整使用习惯来提升正能量');
        } else {
            suggestions.push('建议考虑优化号码或通过其他方式来平衡能量');
        }
        
        if (basicAnalysis.energyBalance.negative > basicAnalysis.energyBalance.positive) {
            suggestions.push('负面磁场较多，建议多做善事，保持积极心态');
        }
        
        return suggestions.join('；');
    }

    // 生成本地建议列表
    generateLocalRecommendations(basicAnalysis) {
        const recs = ['定期清理手机，保持设备和心境清洁'];
        
        if (basicAnalysis.overallScore >= 70) {
            recs.push('在重要决策时可以使用此号码增强运势');
            recs.push('适合在商务场合和社交活动中使用');
        } else {
            recs.push('避免在重要时刻单独依赖此号码');
            recs.push('可以配合其他改运方法提升整体能量');
        }
        
        return recs;
    }

    // 获取评分描述
    getScoreDescription(score) {
        if (score >= 85) return '能量非常旺盛，是一个非常吉祥的号码';
        if (score >= 70) return '能量较好，总体比较吉利';
        if (score >= 55) return '能量一般，有改善空间';
        return '能量不佳，建议考虑调整';
    }

    // 完整的AI分析流程
    async analyzePhoneNumber(phoneNumber, basicAnalysis) {
        this.isAnalyzing = true;
        
        try {
            console.log('开始AI手机号分析:', phoneNumber);
            
            let aiAnalysis;
            
            // 尝试AI分析
            if (window.AI_CONFIG && window.AI_CONFIG.SERVICE_TYPE !== 'offline') {
                try {
                    aiAnalysis = await this.generateAIAnalysis(phoneNumber, basicAnalysis);
                    aiAnalysis.method = 'ai';
                } catch (error) {
                    console.warn('AI分析失败，使用本地分析:', error);
                    aiAnalysis = this.generateLocalAnalysis(phoneNumber, basicAnalysis);
                }
            } else {
                aiAnalysis = this.generateLocalAnalysis(phoneNumber, basicAnalysis);
            }
            
            // 整合结果
            const result = {
                phoneNumber: phoneNumber,
                basicAnalysis: basicAnalysis,
                aiAnalysis: aiAnalysis,
                combinedScore: Math.round((basicAnalysis.overallScore + aiAnalysis.score) / 2),
                generatedAt: new Date().toISOString()
            };
            
            this.lastAnalysis = result;
            return result;
            
        } catch (error) {
            console.error('手机号AI分析失败:', error);
            throw error;
        } finally {
            this.isAnalyzing = false;
        }
    }

    // 生成改运方案
    generateImprovementPlan(analysis) {
        const plan = {
            immediate: [], // 立即可行的建议
            shortTerm: [], // 短期建议（1-3个月）
            longTerm: [], // 长期建议（半年以上）
            forbidden: [] // 注意事项
        };

        const score = analysis.combinedScore;
        
        // 立即可行的建议
        plan.immediate.push('保持手机清洁，定期清理无用信息');
        plan.immediate.push('在重要通话前深呼吸，保持正面情绪');
        
        if (score >= 70) {
            plan.immediate.push('在重要场合优先使用此号码');
            plan.immediate.push('可以设置为主要联系号码');
        } else {
            plan.immediate.push('避免在重要决策时单独依赖此号码');
        }

        // 短期建议
        plan.shortTerm.push('观察使用此号码期间的运势变化');
        plan.shortTerm.push('可以配合风水布局来增强正能量');
        
        if (analysis.basicAnalysis.energyBalance.negative > analysis.basicAnalysis.energyBalance.positive) {
            plan.shortTerm.push('多做善事，积累正面能量');
            plan.shortTerm.push('佩戴适合的护身符或饰品');
        }

        // 长期建议
        plan.longTerm.push('定期进行数字能量分析，跟踪运势变化');
        
        if (score < 60) {
            plan.longTerm.push('考虑更换为能量更好的号码');
            plan.longTerm.push('咨询专业人士制定个性化改运方案');
        } else {
            plan.longTerm.push('保持现有号码，关注其他风水要素');
        }

        // 注意事项
        if (score < 70) {
            plan.forbidden.push('不宜在重要合同签署时使用');
            plan.forbidden.push('避免在心情低落时过度依赖号码运势');
        }
        
        plan.forbidden.push('不要盲目相信，需结合实际行动');
        plan.forbidden.push('避免频繁更换号码，影响稳定性');

        return plan;
    }
}

// 导出模块
window.PhoneNumberAI = PhoneNumberAI; 