<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>✨ 八字精批 - 易海堂算命网</title>
    <link rel="stylesheet" href="../../css/main.css">
    <script src="../../js/modules/api-order.js"></script>
    <script src="../../js/modules/order-payment.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #4A1A1A, #6B2C2C, #8B3A3A);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            color: #D4AF37;
        }
        
        .bazi-container {
            position: relative;
            min-height: 100vh;
            overflow: hidden;
        }
        
        .bazi-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .bazi-content {
            position: relative;
            z-index: 2;
            padding: 0;
        }
        
        .bazi-header {
            background: linear-gradient(135deg, #2C1810, #4A1A1A);
            color: #FFD700;
            text-align: center;
            padding: 20px;
            position: relative;
            border-bottom: 2px solid #D4AF37;
        }
        
        .bazi-header h1 {
            margin: 0;
            font-size: 24px;
            position: relative;
            z-index: 1;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }
        
        .back-btn {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(212, 175, 55, 0.2);
            border: 1px solid #D4AF37;
            color: #FFD700;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            z-index: 2;
        }
        
        .form-container {
            padding: 25px 20px;
            max-width: 500px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            margin-top: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(212, 175, 55, 0.3);
        }
        
        .form-container h3 {
            color: #FFD700;
            text-align: center;
            margin-bottom: 8px;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }
        
        .form-container p {
            color: #D4AF37;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #D4AF37;
            font-weight: 600;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid rgba(212, 175, 55, 0.3);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.95);
            color: #2C1810;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #FFD700;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
        }
        
        .gender-options {
            display: flex;
            gap: 15px;
        }
        
        .gender-option {
            flex: 1;
            text-align: center;
        }
        
        .gender-option input[type="radio"] {
            display: none;
        }
        
        .gender-btn {
            display: block;
            padding: 15px 10px;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(212, 175, 55, 0.3);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            color: #2C1810;
        }
        
        .gender-option input[type="radio"]:checked + .gender-btn {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: white;
            border-color: #FF6347;
            box-shadow: 0 5px 20px rgba(255, 215, 0, 0.4);
        }
        
        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #DC143C, #FF6347, #FFD700);
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            color: white;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(220, 20, 60, 0.4);
        }
        
        .submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(220, 20, 60, 0.6);
        }

        /* 八字日期选择器样式 */
        .bazi-date-picker {
            cursor: pointer;
            border: 2px solid rgba(212, 175, 55, 0.3);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.95);
            transition: all 0.3s ease;
            min-height: 50px;
        }

        .bazi-date-picker:hover {
            border-color: #FFD700;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
        }

        .date-input-display {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 15px;
        }

        .date-placeholder {
            color: #999;
            font-size: 15px;
        }

        .date-placeholder.selected {
            color: #2C1810;
            font-weight: 500;
        }

        .date-picker-btn {
            width: 32px;
            height: 24px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
        }

        .date-picker-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 3px 8px rgba(255, 215, 0, 0.4);
        }

        .picker-icon {
            font-size: 14px;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* 八字日期选择器模态框样式 */
        .bazi-date-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 10001;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .bazi-modal-container {
            background: white;
            border-radius: 15px;
            width: 100%;
            max-width: 380px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideUp 0.3s ease-out;
        }

        @keyframes modalSlideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 金色头部 */
        .bazi-modal-header {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header-icon {
            font-size: 24px;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .bazi-modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* 日期输入框 */
        .date-input-container {
            padding: 20px;
            background: white;
        }

        .date-input-box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 12px 15px;
        }

        .date-value {
            color: #666;
            font-size: 16px;
            font-weight: 500;
        }

        .date-value.selected {
            color: #333;
        }

        .date-confirm-btn {
            width: 36px;
            height: 28px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
        }

        .date-confirm-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 3px 8px rgba(255, 215, 0, 0.4);
        }

        .confirm-icon {
            color: white;
            font-size: 16px;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* 月份导航 */
        .month-navigation {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: white;
            border-bottom: 1px solid #f0f0f0;
        }

        .nav-btn {
            width: 32px;
            height: 32px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            color: #666;
        }

        .nav-btn:hover {
            background: #f8f9fa;
            border-color: #bbb;
            color: #333;
        }

        .current-month {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 600;
            color: #333;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .current-month:hover {
            background: #f8f9fa;
        }

        .month-dropdown {
            font-size: 12px;
            color: #999;
            transition: transform 0.3s ease;
        }

        .current-month:hover .month-dropdown {
            transform: rotate(180deg);
        }

        /* 年份选择器样式 */
        .year-selector {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: white;
            z-index: 10;
            border-radius: 15px;
            overflow: hidden;
        }

        .year-selector-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border-bottom: 1px solid #f0f0f0;
        }

        .year-nav-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            color: white;
            font-weight: bold;
        }

        .year-nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        #yearRangeDisplay {
            font-size: 16px;
            font-weight: 600;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .year-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 8px;
            padding: 20px;
            max-height: 280px;
            overflow-y: auto;
        }

        .year-item {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            color: #333;
            background: #f8f9fa;
            border: 1px solid transparent;
        }

        .year-item:hover {
            background: #FFD700;
            color: #2C1810;
            transform: scale(1.05);
        }

        .year-item.selected {
            background: #FFA500;
            color: white;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(255, 165, 0, 0.3);
        }

        .year-item.current {
            border-color: #FFD700;
            background: rgba(255, 215, 0, 0.1);
            color: #FF6347;
            font-weight: 600;
        }

        .year-selector-footer {
            display: flex;
            justify-content: center;
            padding: 15px 20px;
            background: white;
            border-top: 1px solid #f0f0f0;
        }

        .year-btn {
            padding: 8px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #FFD700;
            color: #2C1810;
        }

        .year-btn:hover {
            background: #FFA500;
            transform: translateY(-1px);
        }

        /* 日历容器 */
        .calendar-container {
            padding: 0 20px 20px;
            background: white;
        }

        /* 星期标题 */
        .week-header {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 0;
            margin-bottom: 10px;
        }

        .week-day {
            padding: 8px 0;
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            color: #666;
        }

        /* 日历网格 */
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 2px;
        }

        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.3s ease;
            color: #333;
            background: white;
            border: 1px solid transparent;
        }

        .calendar-day:hover {
            background: #f8f9fa;
            border-color: #e0e0e0;
        }

        .calendar-day.other-month {
            color: #ccc;
        }

        .calendar-day.selected {
            background: #FFD700;
            color: #2C1810;
            font-weight: 600;
        }

        .calendar-day.today {
            border-color: #FFD700;
            background: rgba(255, 215, 0, 0.1);
            color: #FF6347;
            font-weight: 600;
        }

        /* 底部按钮 */
        .calendar-footer {
            display: flex;
            justify-content: space-between;
            padding: 15px 20px;
            background: white;
            border-top: 1px solid #f0f0f0;
        }

        .footer-btn {
            padding: 8px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .clear-btn {
            background: transparent;
            color: #007AFF;
        }

        .clear-btn:hover {
            background: rgba(0, 122, 255, 0.1);
        }

        .today-btn {
            background: transparent;
            color: #007AFF;
        }

        .today-btn:hover {
            background: rgba(0, 122, 255, 0.1);
        }

        /* 收起箭头 */
        .collapse-arrow {
            display: flex;
            justify-content: center;
            padding: 10px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .collapse-arrow:hover {
            background: #f8f9fa;
        }

        .collapse-arrow span {
            color: #999;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="bazi-container">
        <!-- Canvas动画背景 -->
        <canvas id="baziCanvas" class="bazi-canvas"></canvas>
        
        <div class="bazi-content">
            <!-- 页面头部 -->
            <header class="bazi-header">
                <button class="back-btn" onclick="history.back()">← 返回</button>
                <h1>✨ 八字精批</h1>
            </header>
            
            <!-- 表单内容 -->
            <div class="form-container">
                <h3>📿 请输入您的生辰信息</h3>
                <p>准确的生辰八字，助您洞察命运玄机</p>
                
                <form id="baziForm">
                    <div class="form-group">
                        <label>👤 姓名</label>
                        <input type="text" name="userName" placeholder="请输入您的姓名" required>
                    </div>
                    
                    <div class="form-group">
                        <label>👥 性别</label>
                        <div class="gender-options">
                            <label class="gender-option">
                                <input type="radio" name="gender" value="male" required>
                                <span class="gender-btn">♂ 男</span>
                            </label>
                            <label class="gender-option">
                                <input type="radio" name="gender" value="female" required>
                                <span class="gender-btn">♀ 女</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>📅 请选择您的农历出生日期</label>
                        <div class="bazi-date-picker" id="baziDatePicker" onclick="openBaziDatePicker()">
                            <div class="date-input-display">
                                <span class="date-placeholder" id="dateDisplayText">请选择日期</span>
                                <div class="date-picker-btn">
                                    <span class="picker-icon">📅</span>
                                </div>
                            </div>
                        </div>
                        <!-- 隐藏的输入字段存储选中的值 -->
                        <input type="hidden" name="birthDate" id="birthDate" required>
                        <input type="hidden" id="selectedYear" name="selectedYear">
                        <input type="hidden" id="selectedMonth" name="selectedMonth">
                        <input type="hidden" id="selectedDay" name="selectedDay">
                    </div>
                    
                    <div class="form-group">
                        <label>⏰ 出生时辰</label>
                        <select name="birthHour" required>
                            <option value="">请选择时辰</option>
                            <option value="zi">子时 (23:00-01:00)</option>
                            <option value="chou">丑时 (01:00-03:00)</option>
                            <option value="yin">寅时 (03:00-05:00)</option>
                            <option value="mao">卯时 (05:00-07:00)</option>
                            <option value="chen">辰时 (07:00-09:00)</option>
                            <option value="si">巳时 (09:00-11:00)</option>
                            <option value="wu">午时 (11:00-13:00)</option>
                            <option value="wei">未时 (13:00-15:00)</option>
                            <option value="shen">申时 (15:00-17:00)</option>
                            <option value="you">酉时 (17:00-19:00)</option>
                            <option value="xu">戌时 (19:00-21:00)</option>
                            <option value="hai">亥时 (21:00-23:00)</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="submit-btn">✨ 开始八字精批</button>
                </form>
            </div>

            <!-- AI分析结果区域 -->
            <div id="aiAnalysisSection" class="analysis-section" style="display: none;">
                <div class="analysis-header">
                    <h3>八字精批结果</h3>
                    <div class="analysis-score">
                        <span class="score-label">综合评分：</span>
                        <span id="baziScore" class="score-value">--</span>
                        <span class="score-unit">分</span>
                    </div>
                </div>

                <div class="bazi-display">
                    <h4>📊 八字排盘</h4>
                    <div class="bazi-pillars">
                        <div class="pillar">
                            <span class="pillar-label">年柱</span>
                            <span id="yearPillar" class="pillar-value">--</span>
                        </div>
                        <div class="pillar">
                            <span class="pillar-label">月柱</span>
                            <span id="monthPillar" class="pillar-value">--</span>
                        </div>
                        <div class="pillar">
                            <span class="pillar-label">日柱</span>
                            <span id="dayPillar" class="pillar-value">--</span>
                        </div>
                        <div class="pillar">
                            <span class="pillar-label">时柱</span>
                            <span id="hourPillar" class="pillar-value">--</span>
                        </div>
                    </div>
                </div>

                <div class="analysis-summary">
                    <h4>📝 命理概述</h4>
                    <div id="analysisSummary" class="summary-content">正在分析中...</div>
                </div>

                <div class="analysis-tabs">
                    <div class="tab-buttons">
                        <button class="tab-btn active" data-tab="pattern">格局分析</button>
                        <button class="tab-btn" data-tab="character">性格特征</button>
                        <button class="tab-btn" data-tab="career">事业运势</button>
                        <button class="tab-btn" data-tab="wealth">财运分析</button>
                        <button class="tab-btn" data-tab="marriage">感情婚姻</button>
                        <button class="tab-btn" data-tab="health">健康运势</button>
                        <button class="tab-btn" data-tab="timing">流年运势</button>
                        <button class="tab-btn" data-tab="enhancement">开运建议</button>
                    </div>
                    
                    <div class="tab-contents">
                        <div id="tab-pattern" class="tab-content active">分析中...</div>
                        <div id="tab-character" class="tab-content">分析中...</div>
                        <div id="tab-career" class="tab-content">分析中...</div>
                        <div id="tab-wealth" class="tab-content">分析中...</div>
                        <div id="tab-marriage" class="tab-content">分析中...</div>
                        <div id="tab-health" class="tab-content">分析中...</div>
                        <div id="tab-timing" class="tab-content">分析中...</div>
                        <div id="tab-enhancement" class="tab-content">分析中...</div>
                    </div>
                </div>

                <div class="recommendations-section">
                    <h4>💡 专业建议</h4>
                    <div id="recommendationsList" class="recommendations-list">
                        <div class="recommendation-item">正在生成建议...</div>
                    </div>
                </div>

                <div class="lucky-elements-section">
                    <h4>🍀 开运要素</h4>
                    <div id="luckyElementsList" class="lucky-elements-list">
                        <span class="lucky-element">分析中...</span>
                    </div>
                </div>

                <div class="analysis-footer">
                    <div class="analysis-info" style="display: none;">
                        <span class="analysis-method">分析方式：<span id="analysisMethod">--</span></span>
                        <span class="analysis-time">分析时间：<span id="analysisTime">--</span></span>
                    </div>
                    <button class="regenerate-btn" onclick="regenerateAnalysis()">🔄 重新分析</button>
                </div>
            </div>
        </div>
    </div>

    <style>
        .analysis-section {
            max-width: 800px;
            margin: 20px auto;
            padding: 25px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            border: 1px solid rgba(212, 175, 55, 0.3);
        }

        .analysis-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(212, 175, 55, 0.3);
        }

        .analysis-header h3 {
            color: #FFD700;
            margin: 0;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .analysis-score {
            color: #D4AF37;
        }

        .score-value {
            font-size: 24px;
            font-weight: bold;
            color: #FFD700;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .bazi-display {
            margin-bottom: 25px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .bazi-display h4 {
            color: #FFD700;
            margin-bottom: 15px;
            text-align: center;
        }

        .bazi-pillars {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            text-align: center;
        }

        .pillar {
            padding: 15px;
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(255, 215, 0, 0.1));
            border-radius: 10px;
            border: 1px solid rgba(212, 175, 55, 0.3);
        }

        .pillar-label {
            display: block;
            color: #D4AF37;
            font-size: 12px;
            margin-bottom: 5px;
        }

        .pillar-value {
            display: block;
            color: #FFD700;
            font-size: 18px;
            font-weight: bold;
        }

        .analysis-summary {
            margin-bottom: 25px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .analysis-summary h4 {
            color: #FFD700;
            margin-bottom: 15px;
        }

        .summary-content {
            color: #D4AF37;
            line-height: 1.6;
        }

        .analysis-tabs {
            margin-bottom: 25px;
        }

        .tab-buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }

        .tab-btn {
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 8px;
            color: #D4AF37;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .tab-btn.active,
        .tab-btn:hover {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: white;
            border-color: #FF6347;
        }

        .tab-contents {
            min-height: 200px;
        }

        .tab-content {
            display: none;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            color: #D4AF37;
            line-height: 1.6;
        }

        .tab-content.active {
            display: block;
        }

        .recommendations-section, .lucky-elements-section {
            margin-bottom: 25px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .recommendations-section h4, .lucky-elements-section h4 {
            color: #FFD700;
            margin-bottom: 15px;
        }

        .recommendations-list {
            display: grid;
            gap: 10px;
        }

        .recommendation-item {
            padding: 12px 15px;
            background: rgba(212, 175, 55, 0.1);
            border-left: 4px solid #FFD700;
            border-radius: 5px;
            color: #D4AF37;
        }

        .lucky-elements-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .lucky-element {
            padding: 8px 15px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: white;
            border-radius: 20px;
            font-size: 14px;
        }

        .analysis-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid rgba(212, 175, 55, 0.3);
        }

        .analysis-info {
            display: flex;
            gap: 20px;
            color: #D4AF37;
            font-size: 14px;
        }

        .regenerate-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #DC143C, #FF6347);
            border: none;
            border-radius: 20px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .regenerate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 20, 60, 0.4);
        }

        .loading-animation {
            text-align: center;
            padding: 40px;
            color: #FFD700;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 215, 0, 0.2);
            border-top: 4px solid #FFD700;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .tab-buttons {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .bazi-pillars {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .analysis-footer {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>

    <script src="../../js/modules/ai-service.js"></script>
    <script src="../../js/modules/bazi-ai.js"></script>
    <script src="../../js/modules/api-order.js"></script>
    <script src="../../js/modules/order-payment.js"></script>
    <script src="../../js/modules/unified-auth-service.js"></script>
    <script src="../../js/modules/member-service.js"></script>
    <script>
        console.log('✨ 八字精批页面已加载');
        
        let baziAI = null;
        let currentAnalysis = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeBaziAI();
            setupEventListeners();
        });

        // 初始化八字AI
        function initializeBaziAI() {
            try {
                // 初始化AI服务
                window.aiService = new AIService({
                    SERVICE_TYPE: 'deepseek',
                    DEEPSEEK_API_KEY: '***********************************'
                });
                console.log('✅ AI服务初始化成功');
                
                // 初始化八字AI
                baziAI = new BaziAnalysisAI();
                console.log('✅ 八字AI初始化成功');
                
                // 初始化认证服务
                window.unifiedAuthService = new UnifiedAuthService();
                window.memberService = new MemberService();
                window.orderPaymentManager = new OrderPaymentManager();
                console.log('✅ 认证服务初始化成功');
            } catch (error) {
                console.error('❌ 八字AI初始化失败:', error);
            }
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 表单提交 - 使用新的订单支付版本
            document.getElementById('baziForm').addEventListener('submit', handleFormSubmitWithPayment);
            
            // 标签页切换
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    switchTab(this.dataset.tab);
                });
            });
        }

        // 处理表单提交（带订单支付）
        async function handleFormSubmitWithPayment(e) {
            e.preventDefault();
            
            const form = event.target;
            const formData = new FormData(form);
            const userData = {
                userName: formData.get('userName'),
                gender: formData.get('gender'),
                birthDate: formData.get('birthDate'),
                birthHour: formData.get('birthHour')
            };

            // 验证表单数据
            if (!userData.userName || !userData.gender || !userData.birthDate || !userData.birthHour) {
                alert('请填写完整的生辰信息！');
                return;
            }

            // 服务配置
            const serviceConfig = {
                type: 'bazi',
                name: '八字精批',
                price: 19.9,
                description: '深度解析生辰八字，预测人生运势'
            };

            // 创建订单并支付
            try {
                await window.orderPaymentManager.createOrderAndPay(
                    serviceConfig,
                    userData,
                    // 支付成功回调
                    async function(order, paymentResult) {
                        console.log('支付成功，开始AI分析');
                        await performBaziAnalysis(userData);
                    },
                    // 取消支付回调
                    function(order) {
                        console.log('用户取消支付');
                    }
                );
            } catch (error) {
                console.error('订单创建失败:', error);
                alert('创建订单失败，请稍后重试');
            }
        }

        // 执行八字分析
        async function performBaziAnalysis(userData) {
            try {
                console.log('🚀 开始八字精批分析...', userData);
                
                // 显示分析区域和加载动画
                showAnalysisSection();
                showLoadingAnimation();

                // 调用AI分析
                const result = await baziAI.analyzeBaziWithAI(userData);
                
                if (result && result.success) {
                    currentAnalysis = result.baziAnalysis;
                    displayAnalysisResults(result.baziAnalysis);
                    console.log('✅ 八字分析完成');
                } else {
                    throw new Error('分析失败');
                }
                
            } catch (error) {
                console.error('❌ 八字分析失败:', error);
                showErrorMessage('分析过程中出现错误，请稍后重试');
            }
        }

        // 显示分析区域
        function showAnalysisSection() {
            const section = document.getElementById('aiAnalysisSection');
            section.style.display = 'block';
            section.scrollIntoView({ behavior: 'smooth' });
        }

        // 显示加载动画
        function showLoadingAnimation() {
            const content = `
                <div class="loading-animation">
                    <div class="loading-spinner"></div>
                    <div>🔮 正在精密分析您的八字命理...</div>
                    <div style="margin-top: 10px; font-size: 14px; color: #D4AF37;">
                        正在计算天干地支，分析五行配置...
                    </div>
                </div>
            `;
            
            document.getElementById('analysisSummary').innerHTML = content;
            
            // 清空其他区域
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.innerHTML = '<div class="loading-animation"><div class="loading-spinner"></div><div>正在分析中...</div></div>';
            });
            
            document.getElementById('recommendationsList').innerHTML = '<div class="recommendation-item">正在生成专业建议...</div>';
            document.getElementById('luckyElementsList').innerHTML = '<span class="lucky-element">分析中...</span>';
        }

        // 显示分析结果
        function displayAnalysisResults(analysis) {
            console.log('📊 显示分析结果:', analysis);

            // 显示评分
            document.getElementById('baziScore').textContent = analysis.score || '--';

            // 显示八字排盘
            displayBaziPillars(analysis.bazi);

            // 显示概述
            document.getElementById('analysisSummary').innerHTML = analysis.summary || '暂无概述';

            // 显示各个维度的分析
            displayAnalysisSections(analysis.sections);

            // 显示建议
            displayRecommendations(analysis.recommendations);

            // 显示开运要素
            displayLuckyElements(analysis.luckyElements);

            // 显示分析信息
            updateAnalysisInfo(analysis);
        }

        // 显示八字排盘
        function displayBaziPillars(bazi) {
            const baziKnowledge = baziAI.baziKnowledge;
            
            if (bazi.year && bazi.year.gan && bazi.year.zhi) {
                const yearGan = baziKnowledge.tiangan[bazi.year.gan]?.name || bazi.year.gan;
                const yearZhi = baziKnowledge.dizhi[bazi.year.zhi]?.name || bazi.year.zhi;
                document.getElementById('yearPillar').textContent = yearGan + yearZhi;
            }
            
            if (bazi.month && bazi.month.gan && bazi.month.zhi) {
                const monthGan = baziKnowledge.tiangan[bazi.month.gan]?.name || bazi.month.gan;
                const monthZhi = baziKnowledge.dizhi[bazi.month.zhi]?.name || bazi.month.zhi;
                document.getElementById('monthPillar').textContent = monthGan + monthZhi;
            }
            
            if (bazi.day && bazi.day.gan && bazi.day.zhi) {
                const dayGan = baziKnowledge.tiangan[bazi.day.gan]?.name || bazi.day.gan;
                const dayZhi = baziKnowledge.dizhi[bazi.day.zhi]?.name || bazi.day.zhi;
                document.getElementById('dayPillar').textContent = dayGan + dayZhi;
            }
            
            if (bazi.hour && bazi.hour.gan && bazi.hour.zhi) {
                const hourGan = baziKnowledge.tiangan[bazi.hour.gan]?.name || bazi.hour.gan;
                const hourZhi = baziKnowledge.dizhi[bazi.hour.zhi]?.name || bazi.hour.zhi;
                document.getElementById('hourPillar').textContent = hourGan + hourZhi;
            }
        }

        // 显示分析章节
        function displayAnalysisSections(sections) {
            Object.keys(sections).forEach(key => {
                const element = document.getElementById(`tab-${key}`);
                if (element && sections[key]) {
                    element.innerHTML = sections[key];
                }
            });
        }

        // 显示建议
        function displayRecommendations(recommendations) {
            if (!recommendations || recommendations.length === 0) {
                document.getElementById('recommendationsList').innerHTML = '<div class="recommendation-item">暂无建议</div>';
                return;
            }

            const html = recommendations.map(rec => 
                `<div class="recommendation-item">${rec}</div>`
            ).join('');
            
            document.getElementById('recommendationsList').innerHTML = html;
        }

        // 显示开运要素
        function displayLuckyElements(elements) {
            if (!elements || elements.length === 0) {
                document.getElementById('luckyElementsList').innerHTML = '<span class="lucky-element">暂无要素</span>';
                return;
            }

            const html = elements.map(element => 
                `<span class="lucky-element">${element}</span>`
            ).join('');
            
            document.getElementById('luckyElementsList').innerHTML = html;
        }

        // 更新分析信息
        function updateAnalysisInfo(analysis) {
            document.getElementById('analysisMethod').textContent = analysis.method === 'ai' ? 'AI智能分析' : '本地分析';
            
            if (analysis.timestamp) {
                const time = new Date(analysis.timestamp).toLocaleString('zh-CN');
                document.getElementById('analysisTime').textContent = time;
            }
        }

        // 切换标签页
        function switchTab(tabName) {
            // 更新按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // 更新内容显示
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`tab-${tabName}`).classList.add('active');
        }

        // 重新分析
        function regenerateAnalysis() {
            if (currentAnalysis) {
                console.log('🔄 重新生成分析...');
                showLoadingAnimation();
                
                // 模拟重新分析
                setTimeout(() => {
                    displayAnalysisResults(currentAnalysis);
                }, 2000);
            }
        }

        // 显示错误信息
        function showErrorMessage(message) {
            const content = `
                <div style="text-align: center; padding: 40px; color: #FF6B6B;">
                    <div style="font-size: 48px; margin-bottom: 20px;">❌</div>
                    <div style="font-size: 18px; margin-bottom: 10px;">分析失败</div>
                    <div style="color: #D4AF37;">${message}</div>
                </div>
            `;
            
            document.getElementById('analysisSummary').innerHTML = content;
        }

        // 在页面初始化时添加日期选择器初始化
        document.addEventListener('DOMContentLoaded', function() {
            initBaziDatePicker();
        });

        // ====== 八字日期选择器功能 ======

        // 八字日期选择器变量
        let currentYear = new Date().getFullYear();
        let currentMonth = new Date().getMonth() + 1;
        let selectedDate = null;
        // 年份选择器变量
        let yearRangeStart = Math.floor(currentYear / 10) * 10;

        // 初始化八字日期选择器
        function initBaziDatePicker() {
            try {
                const today = new Date();
                currentYear = today.getFullYear();
                currentMonth = today.getMonth() + 1;
                
                // 添加点击背景关闭功能
                const modal = document.getElementById('baziDateModal');
                if (modal) {
                    modal.addEventListener('click', function(e) {
                        if (e.target === modal) {
                            closeBaziDatePicker();
                        }
                    });
                }
                
                console.log('🗓️ 八字日期选择器初始化完成');
            } catch (error) {
                console.error('❌ 八字日期选择器初始化失败:', error);
            }
        }

        // 打开八字日期选择器
        function openBaziDatePicker() {
            const modal = document.getElementById('baziDateModal');
            const baziDatePicker = document.getElementById('baziDatePicker');
            
            if (modal) {
                modal.style.display = 'flex';
                if (baziDatePicker) baziDatePicker.classList.add('active');
                
                // 生成日历
                generateCalendar();
                updateMonthDisplay();
                
                console.log('📅 八字日期选择器已打开');
            }
        }

        // 关闭八字日期选择器
        function closeBaziDatePicker() {
            const modal = document.getElementById('baziDateModal');
            const baziDatePicker = document.getElementById('baziDatePicker');
            
            if (modal) {
                modal.style.display = 'none';
                if (baziDatePicker) baziDatePicker.classList.remove('active');
                
                console.log('📅 八字日期选择器已关闭');
            }
        }

        // 上一个月
        function prevMonth() {
            currentMonth--;
            if (currentMonth < 1) {
                currentMonth = 12;
                currentYear--;
            }
            generateCalendar();
            updateMonthDisplay();
        }

        // 下一个月
        function nextMonth() {
            currentMonth++;
            if (currentMonth > 12) {
                currentMonth = 1;
                currentYear++;
            }
            generateCalendar();
            updateMonthDisplay();
        }

        // 生成日历
        function generateCalendar() {
            const calendarGrid = document.getElementById('calendarGrid');
            if (!calendarGrid) return;
            
            calendarGrid.innerHTML = '';
            
            // 获取当前月份第一天
            const firstDay = new Date(currentYear, currentMonth - 1, 1);
            // 获取当前月份最后一天
            const lastDay = new Date(currentYear, currentMonth, 0);
            // 获取上个月最后一天
            const prevLastDay = new Date(currentYear, currentMonth - 1, 0);
            
            // 计算第一天是星期几（0=周日，1=周一...）
            let firstDayOfWeek = firstDay.getDay();
            if (firstDayOfWeek === 0) firstDayOfWeek = 7; // 调整为周一开始
            
            // 添加上个月的日期
            for (let i = firstDayOfWeek - 1; i > 0; i--) {
                const day = prevLastDay.getDate() - i + 1;
                const dayElement = createDayElement(day, true, currentMonth === 1 ? currentYear - 1 : currentYear, currentMonth === 1 ? 12 : currentMonth - 1);
                calendarGrid.appendChild(dayElement);
            }
            
            // 添加当前月份的日期
            for (let day = 1; day <= lastDay.getDate(); day++) {
                const dayElement = createDayElement(day, false, currentYear, currentMonth);
                calendarGrid.appendChild(dayElement);
            }
            
            // 添加下个月的日期（填满42格）
            const totalCells = 42;
            const filledCells = calendarGrid.children.length;
            const remainingCells = totalCells - filledCells;
            
            for (let day = 1; day <= remainingCells; day++) {
                const dayElement = createDayElement(day, true, currentMonth === 12 ? currentYear + 1 : currentYear, currentMonth === 12 ? 1 : currentMonth + 1);
                calendarGrid.appendChild(dayElement);
            }
        }

        // 创建日期元素
        function createDayElement(day, isOtherMonth, year, month) {
            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day';
            dayElement.textContent = day;
            dayElement.dataset.year = year;
            dayElement.dataset.month = month;
            dayElement.dataset.day = day;
            
            if (isOtherMonth) {
                dayElement.classList.add('other-month');
            }
            
            // 检查是否为今天
            const today = new Date();
            if (year === today.getFullYear() && month === today.getMonth() + 1 && day === today.getDate()) {
                dayElement.classList.add('today');
            }
            
            // 检查是否为选中日期
            if (selectedDate && selectedDate.year === year && selectedDate.month === month && selectedDate.day === day) {
                dayElement.classList.add('selected');
            }
            
            // 添加点击事件
            dayElement.addEventListener('click', () => {
                selectDate(year, month, day);
            });
            
            return dayElement;
        }

        // 更新月份显示
        function updateMonthDisplay() {
            const monthDisplay = document.getElementById('currentMonthDisplay');
            if (monthDisplay) {
                monthDisplay.textContent = `${currentYear}年${currentMonth.toString().padStart(2, '0')}月`;
            }
        }

        // 选择日期
        function selectDate(year, month, day) {
            selectedDate = { year, month, day };
            
            // 更新隐藏字段
            const birthDate = document.getElementById('birthDate');
            if (birthDate) {
                birthDate.value = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
            }
            
            // 更新显示
            updateDateDisplay();
            updateModalDateDisplay();
            
            // 重新生成日历以更新选中状态
            generateCalendar();
            
            // 延迟关闭模态框
            setTimeout(() => {
                closeBaziDatePicker();
            }, 300);
        }

        // 清除日期
        function clearDate() {
            selectedDate = null;
            
            const birthDate = document.getElementById('birthDate');
            if (birthDate) birthDate.value = '';
            
            updateDateDisplay();
            updateModalDateDisplay();
            generateCalendar();
            
            setTimeout(() => {
                closeBaziDatePicker();
            }, 300);
        }

        // 选择今天
        function selectToday() {
            const today = new Date();
            const year = today.getFullYear();
            const month = today.getMonth() + 1;
            const day = today.getDate();
            
            currentYear = year;
            currentMonth = month;
            
            selectDate(year, month, day);
            updateMonthDisplay();
            generateCalendar();
        }

        // 确认日期选择
        function confirmDate() {
            if (selectedDate) {
                closeBaziDatePicker();
            } else {
                alert('请先选择一个日期');
            }
        }

        // 更新日期显示（主界面）
        function updateDateDisplay() {
            const dateDisplayText = document.getElementById('dateDisplayText');
            if (dateDisplayText) {
                if (selectedDate) {
                    const { year, month, day } = selectedDate;
                    dateDisplayText.textContent = `${year}/${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`;
                    dateDisplayText.classList.add('selected');
                } else {
                    dateDisplayText.textContent = '请选择日期';
                    dateDisplayText.classList.remove('selected');
                }
            }
        }

        // 更新模态框日期显示
        function updateModalDateDisplay() {
            const modalDateDisplay = document.getElementById('modalDateDisplay');
            if (modalDateDisplay) {
                if (selectedDate) {
                    const { year, month, day } = selectedDate;
                    modalDateDisplay.textContent = `${year}/${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`;
                    modalDateDisplay.classList.add('selected');
                } else {
                    modalDateDisplay.textContent = '请选择日期';
                    modalDateDisplay.classList.remove('selected');
                }
            }
        }

        // 显示年份选择器
        function showYearSelector() {
            const yearSelector = document.getElementById('yearSelector');
            const calendarContainer = document.querySelector('.calendar-container');
            const calendarFooter = document.querySelector('.calendar-footer');
            const monthNavigation = document.querySelector('.month-navigation');
            
            if (yearSelector) {
                yearSelector.style.display = 'block';
                if (calendarContainer) calendarContainer.style.display = 'none';
                if (calendarFooter) calendarFooter.style.display = 'none';
                if (monthNavigation) monthNavigation.style.display = 'none';
                
                generateYearGrid();
                updateYearRangeDisplay();
            }
        }

        // 隐藏年份选择器
        function hideYearSelector() {
            const yearSelector = document.getElementById('yearSelector');
            const calendarContainer = document.querySelector('.calendar-container');
            const calendarFooter = document.querySelector('.calendar-footer');
            const monthNavigation = document.querySelector('.month-navigation');
            
            if (yearSelector) {
                yearSelector.style.display = 'none';
                if (calendarContainer) calendarContainer.style.display = 'block';
                if (calendarFooter) calendarFooter.style.display = 'flex';
                if (monthNavigation) monthNavigation.style.display = 'flex';
            }
        }

        // 改变年份范围
        function changeYearRange(delta) {
            yearRangeStart += delta;
            if (yearRangeStart < 1900) yearRangeStart = 1900;
            if (yearRangeStart > 2100) yearRangeStart = 2100;
            
            generateYearGrid();
            updateYearRangeDisplay();
        }

        // 更新年份范围显示
        function updateYearRangeDisplay() {
            const yearRangeDisplay = document.getElementById('yearRangeDisplay');
            if (yearRangeDisplay) {
                yearRangeDisplay.textContent = `${yearRangeStart}-${yearRangeStart + 9}`;
            }
        }

        // 生成年份网格
        function generateYearGrid() {
            const yearGrid = document.getElementById('yearGrid');
            if (!yearGrid) return;
            
            yearGrid.innerHTML = '';
            const currentYearValue = new Date().getFullYear();
            
            for (let i = 0; i < 10; i++) {
                const year = yearRangeStart + i;
                const yearItem = document.createElement('div');
                yearItem.className = 'year-item';
                yearItem.textContent = year;
                yearItem.dataset.year = year;
                
                // 添加当前年份标记
                if (year === currentYearValue) {
                    yearItem.classList.add('current');
                }
                
                // 添加选中年份标记
                if (year === currentYear) {
                    yearItem.classList.add('selected');
                }
                
                // 添加点击事件
                yearItem.addEventListener('click', () => {
                    selectYear(year);
                });
                
                yearGrid.appendChild(yearItem);
            }
        }

        // 选择年份
        function selectYear(year) {
            currentYear = year;
            
            // 更新月份显示
            updateMonthDisplay();
            
            // 重新生成日历
            generateCalendar();
            
            // 隐藏年份选择器
            hideYearSelector();
        }

        // 表单提交处理
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('baziForm');
            if (form) {
                form.addEventListener('submit', handleFormSubmitWithPayment);
            }
        });
    </script>

    <!-- 八字日期选择器模态框 -->
    <div id="baziDateModal" class="bazi-date-modal">
        <div class="bazi-modal-container">
            <!-- 金色头部 -->
            <div class="bazi-modal-header">
                <div class="header-icon">✨</div>
                <h3>请选择农历出生日期</h3>
            </div>
            
            <!-- 日期输入框 -->
            <div class="date-input-container">
                <div class="date-input-box">
                    <span class="date-value" id="modalDateDisplay">请选择日期</span>
                    <div class="date-confirm-btn" onclick="confirmDate()">
                        <span class="confirm-icon">✓</span>
                    </div>
                </div>
            </div>
            
            <!-- 月份导航 -->
            <div class="month-navigation">
                <button class="nav-btn prev-month" onclick="prevMonth()">
                    <span>↑</span>
                </button>
                <div class="current-month" onclick="showYearSelector()">
                    <span id="currentMonthDisplay">2025年01月</span>
                    <span class="month-dropdown">▼</span>
                </div>
                <button class="nav-btn next-month" onclick="nextMonth()">
                    <span>↓</span>
                </button>
            </div>

            <!-- 年份选择器 -->
            <div id="yearSelector" class="year-selector" style="display: none;">
                <div class="year-selector-header">
                    <button class="year-nav-btn" onclick="changeYearRange(-10)">‹‹</button>
                    <span id="yearRangeDisplay">2020-2029</span>
                    <button class="year-nav-btn" onclick="changeYearRange(10)">››</button>
                </div>
                <div class="year-grid" id="yearGrid">
                    <!-- 年份选项将通过JavaScript生成 -->
                </div>
                <div class="year-selector-footer">
                    <button class="year-btn" onclick="hideYearSelector()">返回</button>
                </div>
            </div>
            
            <!-- 日历网格 -->
            <div class="calendar-container">
                <!-- 星期标题 -->
                <div class="week-header">
                    <div class="week-day">一</div>
                    <div class="week-day">二</div>
                    <div class="week-day">三</div>
                    <div class="week-day">四</div>
                    <div class="week-day">五</div>
                    <div class="week-day">六</div>
                    <div class="week-day">日</div>
                </div>
                
                <!-- 日历日期网格 -->
                <div class="calendar-grid" id="calendarGrid">
                    <!-- 日期将通过JavaScript动态生成 -->
                </div>
            </div>
            
            <!-- 底部按钮 -->
            <div class="calendar-footer">
                <button class="footer-btn clear-btn" onclick="clearDate()">清除</button>
                <button class="footer-btn today-btn" onclick="selectToday()">今天</button>
            </div>
            
            <!-- 收起箭头 -->
            <div class="collapse-arrow" onclick="closeBaziDatePicker()">
                <span>▼</span>
            </div>
        </div>
    </div>
</body>
</html> 