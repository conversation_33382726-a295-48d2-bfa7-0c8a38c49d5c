# 智谱AI姻缘画像配置指南

## 概述

本项目已集成智谱AI的CogView图像生成服务，用于生成高质量的姻缘画像。本文档将指导您如何配置和使用智谱AI服务。

## 获取智谱AI API密钥

### 1. 注册智谱AI账号
- 访问 [智谱AI开放平台](https://open.bigmodel.cn/)
- 点击"注册"创建新账号
- 完成邮箱验证和实名认证

### 2. 创建API密钥
- 登录后进入控制台
- 在左侧菜单中找到"API密钥"
- 点击"创建新的API密钥"
- 复制生成的API密钥（格式类似：`sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`）

### 3. 充值账户（如需要）
- 智谱AI提供免费额度，新用户通常有一定的免费调用次数
- 如需更多调用次数，可在控制台进行充值

## 配置项目

### 1. 修改配置文件
在 `pages/tarot/index.html` 文件中找到以下配置部分：

```javascript
// AI配置
window.AI_CONFIG = {
    SERVICE_TYPE: 'zhipu', // 使用智谱AI
    
    // 智谱AI配置
    ZHIPU_API_KEY: '', // 在这里填入您的API密钥
    ZHIPU_BASE_URL: 'https://open.bigmodel.cn/api/paas/v4',
    ZHIPU_TEXT_MODEL: 'glm-4-flash',
    ZHIPU_IMAGE_MODEL: 'cogview-3-plus',
    
    // 其他配置...
};
```

### 2. 填入API密钥
将您从智谱AI平台获取的API密钥填入 `ZHIPU_API_KEY` 字段：

```javascript
ZHIPU_API_KEY: 'sk-your-actual-api-key-here',
```

## 模型选择

### 文本模型（用于生成画像描述）
- `glm-4`: 最新的GLM-4模型，功能强大
- `glm-4-flash`: 快速版本，响应更快
- `glm-4-plus`: 增强版本，质量更高

### 图像模型（用于生成画像）
- `cogview-3`: 标准版本
- `cogview-3-plus`: 增强版本，图像质量更高

## 功能特点

### 1. 智能画像描述生成
- 根据用户填写的18步向导信息
- 生成详细的理想伴侣画像描述
- 包含外貌、气质、风格等多维度特征

### 2. 多风格图像生成
- 优雅知性风格
- 活泼可爱风格  
- 成熟稳重风格
- 每次生成3张不同风格的画像

### 3. 智能提示词优化
- 自动将中文描述转换为适合图像生成的英文提示词
- 添加质量提升关键词
- 优化画像生成效果

## 使用流程

1. **用户填写向导**: 用户完成18步详细偏好设置
2. **生成文本描述**: 调用GLM-4生成详细的画像描述
3. **生成图像**: 使用CogView根据描述生成3张不同风格的画像
4. **展示结果**: 在页面上展示生成的画像供用户查看

## 错误处理

### 1. API密钥错误
- 检查API密钥是否正确填写
- 确认API密钥是否有效且未过期
- 检查账户余额是否充足

### 2. 网络连接问题
- 检查网络连接是否正常
- 确认防火墙设置允许访问智谱AI API

### 3. 服务降级
- 如果智谱AI服务不可用，系统会自动降级到其他图像生成服务
- 确保有备用的图像生成方案

## 成本控制

### 1. 调用频率限制
- 在连续调用之间添加1秒延迟
- 避免短时间内大量调用

### 2. 缓存机制
- 可考虑对相似的用户偏好进行结果缓存
- 减少重复的API调用

### 3. 监控使用量
- 定期检查API调用量和费用
- 设置合理的使用限制

## 技术支持

如果在配置或使用过程中遇到问题，可以：

1. 查看浏览器控制台的错误信息
2. 检查智谱AI官方文档
3. 联系技术支持团队

## 更新日志

- **v1.0**: 初始版本，支持基本的画像生成功能
- **v1.1**: 添加多风格支持和错误处理
- **v1.2**: 优化提示词生成和图像质量
