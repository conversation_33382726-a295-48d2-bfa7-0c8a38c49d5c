/**
 * 请求拦截器
 * 用于处理请求认证、令牌刷新等
 */
class RequestInterceptor {
    constructor() {
        this.setup();
    }

    setup() {
        const originalFetch = window.fetch;
        
        window.fetch = async (...args) => {
            const [resource, config] = args;
            
            // 如果是登录或刷新令牌请求，直接发送
            if (this.isAuthRequest(resource)) {
                return originalFetch(resource, config);
            }

            try {
                // 添加认证头
                const configWithAuth = await this.addAuthHeader(config);
                
                // 发送请求
                const response = await originalFetch(resource, configWithAuth);
                
                // 处理认证相关的响应
                if (response.status === 401) {
                    // 尝试刷新令牌
                    const newToken = await this.refreshToken();
                    if (newToken) {
                        // 使用新令牌重试请求
                        const configWithNewAuth = await this.addAuthHeader(config);
                        return originalFetch(resource, configWithNewAuth);
                    }
                } else if (response.status === 403) {
                    // 处理权限不足的情况
                    const error = await response.json();
                    this.handleForbidden(error);
                    throw new Error(error.message || '没有权限访问');
                }
                
                return response;
            } catch (error) {
                // 如果是认证错误，重定向到登录页
                if (error.message === 'Authentication failed') {
                    this.redirectToLogin();
                }
                throw error;
            }
        };
    }

    /**
     * 判断是否是认证相关请求
     * @param {string} url - 请求地址
     * @returns {boolean}
     */
    isAuthRequest(url) {
        const authUrls = [
            '/auth/user_name',
            '/auth/refresh_token',
            '/api/member/login',
            '/api/member/register',
            '/api/member/info',
            '/api/member/check',
            '/unified/auth/login',
            '/unified/auth/admin/login',
            '/unified/auth/member/login',
            '/unified/auth/logout',
            '/unified/auth/refresh_token',
            '/login',
            '/register',
            '/info',
            '/check'
        ];
        return authUrls.some(authUrl => url.includes(authUrl));
    }

    /**
     * 判断当前页面是否是会员页面
     * @returns {boolean}
     */
    isMemberPage() {
        return window.location.pathname.includes('/pages/member/') || 
               window.location.pathname.includes('/member/');
    }

    /**
     * 获取当前认证服务
     * @returns {Object} 认证服务实例
     */
    getAuthService() {
        // 优先使用统一认证服务
        if (window.unifiedAuthService) {
            return window.unifiedAuthService;
        }
        
        // 兼容旧版本
        if (this.isMemberPage()) {
            return window.memberService;
        }
        return window.authService;
    }

    /**
     * 获取当前令牌
     * @returns {string|null} 令牌
     */
    getToken() {
        const authService = this.getAuthService();
        if (!authService) {
            return null;
        }
        
        // 优先使用统一认证服务
        if (window.unifiedAuthService) {
            return window.unifiedAuthService.getToken();
        }
        
        // 兼容旧版本
        if (this.isMemberPage()) {
            // 会员服务使用不同的令牌存储方式
            return localStorage.getItem('member_token');
        } else {
            // 管理员服务使用getToken方法
            return authService.getToken();
        }
    }

    /**
     * 添加认证头
     * @param {Object} config - 请求配置
     * @returns {Object} 新的请求配置
     */
    async addAuthHeader(config = {}) {
        const token = this.getToken();
        
        if (!token) {
            throw new Error('Authentication failed');
        }

        return {
            ...config,
            headers: {
                ...config.headers,
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        };
    }

    /**
     * 刷新令牌
     * @returns {Promise<string>} 新的令牌
     */
    async refreshToken() {
        try {
            const authService = this.getAuthService();
            if (!authService) {
                return null;
            }

            if (this.isMemberPage()) {
                // 会员服务暂时不支持令牌刷新，直接重定向到登录页
                this.redirectToLogin();
                return null;
            } else {
                const result = await authService.refreshToken();
                return result.token;
            }
        } catch (error) {
            console.error('刷新令牌失败:', error);
            this.redirectToLogin();
            return null;
        }
    }

    /**
     * 处理403权限不足的情况
     * @param {Object} error - 错误信息
     */
    handleForbidden(error) {
        // 可以在这里添加提示或其他处理
        console.error('权限不足:', error);
        // 如果需要，可以跳转到无权限页面
        // window.location.href = '/pages/403.html';
    }

    /**
     * 重定向到登录页
     */
    redirectToLogin() {
        const currentPath = window.location.pathname + window.location.search;
        if (this.isMemberPage()) {
            window.location.href = `/pages/member/login/index.html?redirect=${encodeURIComponent(currentPath)}`;
        } else {
            window.location.href = `/pages/login/index.html?redirect=${encodeURIComponent(currentPath)}`;
        }
    }
}

// 创建请求拦截器实例
new RequestInterceptor(); 