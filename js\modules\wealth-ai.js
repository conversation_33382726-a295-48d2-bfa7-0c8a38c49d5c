/**
 * 财运分析AI模块
 * 基于八字和生辰信息进行财运分析
 */

class WealthAnalysisAI {
    constructor() {
        this.initializeKnowledgeBase();
        // 初始化认证服务
        if (window.initializeAuthServices) {
            window.initializeAuthServices();
        }
    }

    // 初始化财运学知识库
    initializeKnowledgeBase() {
        this.wealthKnowledge = {
            // 时辰与财运关系
            hourWealth: {
                zi: { name: '子时', element: '水', wealthLevel: 8, traits: ['夜间财运佳', '适合网络创业'] },
                chou: { name: '丑时', element: '土', wealthLevel: 7, traits: ['积财能力强', '适合稳健投资'] },
                yin: { name: '寅时', element: '木', wealthLevel: 6, traits: ['创业精神强', '适合新兴行业'] },
                mao: { name: '卯时', element: '木', wealthLevel: 7, traits: ['文创财运佳', '适合技能变现'] },
                chen: { name: '辰时', element: '土', wealthLevel: 9, traits: ['财库丰盈', '适合大宗投资'] },
                si: { name: '巳时', element: '火', wealthLevel: 8, traits: ['财运爆发力强', '适合短期投资'] },
                wu: { name: '午时', element: '火', wealthLevel: 9, traits: ['正财运极佳', '事业财运双旺'] },
                wei: { name: '未时', element: '土', wealthLevel: 6, traits: ['财运平稳', '适合合作投资'] },
                shen: { name: '申时', element: '金', wealthLevel: 8, traits: ['商业嗅觉敏锐', '适合贸易投资'] },
                you: { name: '酉时', element: '金', wealthLevel: 7, traits: ['理财能力突出', '适合稳健增值'] },
                xu: { name: '戌时', element: '土', wealthLevel: 5, traits: ['财运较保守', '适合长期储蓄'] },
                hai: { name: '亥时', element: '水', wealthLevel: 7, traits: ['流动财运强', '适合多元投资'] }
            },

            // 五行财星配置
            elements: {
                wood: { name: '木', careers: ['文化创意', '教育培训', '环保科技'], colors: ['绿色', '青色'], numbers: [3, 8], directions: ['东方', '东南'] },
                fire: { name: '火', careers: ['科技电子', '金融投资', '餐饮娱乐'], colors: ['红色', '紫色'], numbers: [2, 7], directions: ['南方'] },
                earth: { name: '土', careers: ['房地产', '农业种植', '建筑工程'], colors: ['黄色', '土黄'], numbers: [5, 0], directions: ['中央', '西南'] },
                metal: { name: '金', careers: ['金融银行', '珠宝首饰', '机械制造'], colors: ['白色', '金色'], numbers: [4, 9], directions: ['西方', '西北'] },
                water: { name: '水', careers: ['互联网', '贸易流通', '旅游服务'], colors: ['黑色', '蓝色'], numbers: [1, 6], directions: ['北方'] }
            },
            
            // 2025年财运趋势
            yearTrends: {
                wood: { trend: '上升', description: '2025年木行业财运上升，教育和创意产业有望获得更多投资机会。' },
                fire: { trend: '稳定', description: '2025年火行业财运稳定，科技和金融投资可获得平稳回报。' },
                earth: { trend: '波动', description: '2025年土行业财运有波动，房地产和建筑需谨慎投资。' },
                metal: { trend: '下降', description: '2025年金行业财运略有下降，传统制造业面临挑战。' },
                water: { trend: '上升', description: '2025年水行业财运强劲上升，互联网和跨境贸易将迎来机遇。' }
            },
            
            // 财运分析维度
            analysisDimensions: [
                '整体财运评估',
                '正财分析',
                '投资理财',
                '财运时机',
                '求财方位',
                '增值建议',
                '开运方法'
            ],
            
            // 财运开运物品
            luckyItems: {
                wood: ['绿色植物', '木质饰品', '翡翠玉石'],
                fire: ['红色装饰', '蜡烛灯具', '电子产品'],
                earth: ['黄水晶', '陶瓷摆件', '山石盆栽'],
                metal: ['金银首饰', '白色装饰', '金属工艺品'],
                water: ['鱼缸水景', '蓝色饰品', '流水装置']
            }
        };
    }

    // 生成AI提示词
    generatePrompt(userData) {
        const { userName, gender, birthDate, birthHour } = userData;
        const hourInfo = this.wealthKnowledge.hourWealth[birthHour];
        const elementName = hourInfo.element;
        const elementKey = this.getElementKey(elementName);
        const yearTrend = this.wealthKnowledge.yearTrends[elementKey] || this.wealthKnowledge.yearTrends.earth;
        
        return `请为${userName}（${gender === 'male' ? '男' : '女'}，出生于${birthDate}，${hourInfo.name}）进行详细的财运分析。

基本信息：
- 性别：${gender === 'male' ? '男' : '女'}
- 出生日期：${birthDate}
- 出生时辰：${hourInfo.name}（${hourInfo.element}）
- 财运等级：${hourInfo.wealthLevel}/10
- 财运特点：${hourInfo.traits.join('，')}
- 2025年${elementName}行业趋势：${yearTrend.trend}

请从以下维度进行详细分析：
1. 整体财运评估：综合分析财运状况，包括先天财运和后天发展
2. 正财运势分析：工作事业收入的发展和建议
3. 投资理财运势：投资方向、风险偏好和理财策略
4. 财运时机把握：何时投资、何时守成的指导
5. 求财方位指导：最适合的财运方位和行业选择
6. 财富增值建议：如何让财富保值增值的实用建议
7. 开运改善方法：提升财运的方法、颜色、数字等

请给出专业、实用、具体的建议，避免空泛的内容。回复采用中文，内容丰富详实。`;
    }

    // AI财运分析
    async analyzeWealthWithAI(userData) {
        try {
            console.log('🔍 检查AI服务状态...');
            console.log('- window.aiService:', !!window.aiService);
            console.log('- AI_CONFIG:', window.AI_CONFIG);
            
            if (!window.aiService) {
                console.log('⚠️ AI服务未初始化，使用本地分析');
                return this.generateLocalAnalysis(userData);
            }

            console.log('🚀 开始AI财运分析...');
            const prompt = this.generatePrompt(userData);
            console.log('📝 生成提示词:', prompt.substring(0, 100) + '...');
            console.log('📏 提示词总长度:', prompt.length);
            
            try {
                const systemPrompt = "你是一个专业的财运分析师，精通传统命理学和现代理财理论，能够进行准确的财运分析和投资指导。请用中文回复，严格按照指定的格式输出分析结果。";
                console.log('🎯 系统提示词:', systemPrompt);
                
                console.log('📞 调用DeepSeek API...');
                const aiResponse = await window.aiService.callDeepSeek(prompt, systemPrompt);
                console.log('✅ AI响应成功，长度:', aiResponse.length);
                console.log('📄 AI响应内容预览:', aiResponse.substring(0, 200) + '...');
                
                const result = this.parseAIResponse(aiResponse, userData);
                console.log('📊 解析后的结果:', result);
                return result;
            } catch (error) {
                console.error('❌ AI调用失败，使用本地分析:', error);
                return this.generateLocalAnalysis(userData);
            }
        } catch (error) {
            console.error('❌ AI财运分析失败:', error);
            // 确保即使出错也返回一个有效的分析结果
            return this.generateLocalAnalysis(userData);
        }
    }

    // 解析AI响应
    parseAIResponse(aiResponse, userData) {
        try {
            console.log('🔍 开始解析AI响应...');
            console.log('📄 AI响应长度:', aiResponse.length);
            console.log('📄 AI响应预览:', aiResponse.substring(0, 300) + '...');
            
            const hourInfo = this.wealthKnowledge.hourWealth[userData.birthHour];
            const score = this.calculateWealthScore(userData);
            
            console.log('📊 基础信息:', {
                hourInfo: hourInfo,
                score: score,
                userData: userData
            });
            
            // 提取各部分内容
            const sections = this.extractSectionsFromResponse(aiResponse);
            console.log('📋 提取的章节:', sections);
            
            // 提取建议
            const recommendations = this.extractRecommendations(aiResponse);
            console.log('💡 提取的建议:', recommendations);
            
            // 提取幸运元素
            const luckyElements = this.extractLuckyElements(aiResponse, hourInfo.element);
            console.log('🍀 提取的幸运元素:', luckyElements);
            
            // 提取摘要
            const summary = this.extractSummary(aiResponse);
            console.log('📝 提取的摘要:', summary);

            const result = {
                success: true,
                wealthAnalysis: {
                    method: 'ai',
                    score: score,
                    summary: summary,
                    sections: sections,
                    recommendations: recommendations,
                    luckyElements: luckyElements,
                    timestamp: new Date().toISOString()
                }
            };
            
            console.log('✅ AI响应解析完成:', result);
            return result;
        } catch (error) {
            console.error('❌ 解析AI响应失败:', error);
            // 如果解析失败，回退到本地分析
            return this.generateLocalAnalysis(userData);
        }
    }
    
    // 从AI响应中提取摘要
    extractSummary(response) {
        try {
            // 尝试找到整体评估部分
            let summary = '';
            const overallMatch = response.match(/整体财运评估[：:]\s*([\s\S]*?)(?=\d+\.|$)/i);
            if (overallMatch && overallMatch[1]) {
                summary = overallMatch[1].trim();
                // 如果太长，截取前200个字符
                if (summary.length > 200) {
                    summary = summary.substring(0, 200) + '...';
                }
            } else {
                // 如果找不到整体评估，就取前200个字符
                summary = response.substring(0, 200) + '...';
            }
            return summary;
        } catch (error) {
            console.error('提取摘要失败:', error);
            return '财运分析结果生成完成，请查看详细内容。';
        }
    }
    
    // 从AI响应中提取各部分内容
    extractSectionsFromResponse(response) {
        try {
            console.log('🔍 开始提取AI响应章节...');
            console.log('📄 响应内容预览:', response.substring(0, 500) + '...');
            
            const sections = {
                overall: '',
                regular: '',
                investment: '',
                timing: '',
                direction: '',
                advice: '',
                enhancement: ''
            };
            
            // 尝试多种匹配模式
            const patterns = [
                // 标准模式
                {
                    overall: /整体财运评估[：:]\s*([\s\S]*?)(?=\d+\.|$)/i,
                    regular: /正财运势分析[：:]\s*([\s\S]*?)(?=\d+\.|$)/i,
                    investment: /投资理财运势[：:]\s*([\s\S]*?)(?=\d+\.|$)/i,
                    timing: /财运时机把握[：:]\s*([\s\S]*?)(?=\d+\.|$)/i,
                    direction: /求财方位指导[：:]\s*([\s\S]*?)(?=\d+\.|$)/i,
                    advice: /财富增值建议[：:]\s*([\s\S]*?)(?=\d+\.|$)/i,
                    enhancement: /开运改善方法[：:]\s*([\s\S]*?)(?=\d+\.|$)/i
                },
                // 带编号的模式
                {
                    overall: /1[\.\s]*整体财运评估[：:]\s*([\s\S]*?)(?=\d+\.|$)/i,
                    regular: /2[\.\s]*正财运势分析[：:]\s*([\s\S]*?)(?=\d+\.|$)/i,
                    investment: /3[\.\s]*投资理财运势[：:]\s*([\s\S]*?)(?=\d+\.|$)/i,
                    timing: /4[\.\s]*财运时机把握[：:]\s*([\s\S]*?)(?=\d+\.|$)/i,
                    direction: /5[\.\s]*求财方位指导[：:]\s*([\s\S]*?)(?=\d+\.|$)/i,
                    advice: /6[\.\s]*财富增值建议[：:]\s*([\s\S]*?)(?=\d+\.|$)/i,
                    enhancement: /7[\.\s]*开运改善方法[：:]\s*([\s\S]*?)(?=\d+\.|$)/i
                },
                // 带星号的模式
                {
                    overall: /🌟\s*整体财运评估[：:]\s*([\s\S]*?)(?=🌟|💼|💎|⏰|🧭|💡|🔮|$)/i,
                    regular: /💼\s*正财运势分析[：:]\s*([\s\S]*?)(?=🌟|💼|💎|⏰|🧭|💡|🔮|$)/i,
                    investment: /💎\s*投资理财运势[：:]\s*([\s\S]*?)(?=🌟|💼|💎|⏰|🧭|💡|🔮|$)/i,
                    timing: /⏰\s*财运时机把握[：:]\s*([\s\S]*?)(?=🌟|💼|💎|⏰|🧭|💡|🔮|$)/i,
                    direction: /🧭\s*求财方位指导[：:]\s*([\s\S]*?)(?=🌟|💼|💎|⏰|🧭|💡|🔮|$)/i,
                    advice: /💡\s*财富增值建议[：:]\s*([\s\S]*?)(?=🌟|💼|💎|⏰|🧭|💡|🔮|$)/i,
                    enhancement: /🔮\s*开运改善方法[：:]\s*([\s\S]*?)(?=🌟|💼|💎|⏰|🧭|💡|🔮|$)/i
                }
            ];
            
            let foundAny = false;
            
            // 尝试每种模式
            for (let patternIndex = 0; patternIndex < patterns.length; patternIndex++) {
                const pattern = patterns[patternIndex];
                console.log(`🔍 尝试模式 ${patternIndex + 1}:`, Object.keys(pattern));
                
                let patternFound = false;
                
                Object.keys(sections).forEach(key => {
                    const regex = pattern[key];
                    if (regex) {
                        const match = response.match(regex);
                        if (match && match[1]) {
                            sections[key] = match[1].trim();
                            patternFound = true;
                            foundAny = true;
                            console.log(`✅ 找到章节 ${key}:`, sections[key].substring(0, 100) + '...');
                        }
                    }
                });
                
                if (patternFound) {
                    console.log(`✅ 模式 ${patternIndex + 1} 匹配成功`);
                    break;
                }
            }
            
            // 如果没有找到任何章节，尝试提取整个响应作为整体评估
            if (!foundAny) {
                console.log('⚠️ 没有找到匹配的章节，使用整个响应作为整体评估');
                sections.overall = response.substring(0, 1000) + '...';
                foundAny = true;
            }
            
            // 确保至少有一个部分有内容
            if (!foundAny) {
                console.log('⚠️ 所有章节都为空，使用默认内容');
                sections.overall = 'AI财运分析已完成，但解析过程中出现异常。请查看详细内容。';
            }
            
            console.log('📋 最终提取的章节:', {
                overall: sections.overall ? '有内容' : '空',
                regular: sections.regular ? '有内容' : '空',
                investment: sections.investment ? '有内容' : '空',
                timing: sections.timing ? '有内容' : '空',
                direction: sections.direction ? '有内容' : '空',
                advice: sections.advice ? '有内容' : '空',
                enhancement: sections.enhancement ? '有内容' : '空'
            });
            
            return sections;
        } catch (error) {
            console.error('❌ 提取内容部分失败:', error);
            return {
                overall: '财运分析结果已生成，但解析过程中出现错误。',
                regular: '',
                investment: '',
                timing: '',
                direction: '',
                advice: '',
                enhancement: ''
            };
        }
    }
    
    // 从AI响应中提取建议
    extractRecommendations(response) {
        const recommendations = [];
        
        // 尝试从财富增值建议和开运改善方法部分提取
        const adviceMatch = response.match(/财富增值建议[：:]\s*([\s\S]*?)(?=\d+\.|$)/i);
        const enhancementMatch = response.match(/开运改善方法[：:]\s*([\s\S]*?)(?=\d+\.|$)/i);
        
        let adviceText = '';
        if (adviceMatch && adviceMatch[1]) {
            adviceText += adviceMatch[1].trim();
        }
        if (enhancementMatch && enhancementMatch[1]) {
            adviceText += ' ' + enhancementMatch[1].trim();
        }
        
        // 从文本中提取建议点
        const bulletPoints = adviceText.match(/[•·\-–—]\s*([^•·\-–—\n]+)/g);
        if (bulletPoints && bulletPoints.length > 0) {
            bulletPoints.forEach(point => {
                const cleanPoint = point.replace(/[•·\-–—]\s*/, '').trim();
                if (cleanPoint && recommendations.length < 6) {
                    recommendations.push(cleanPoint);
                }
            });
        }
        
        // 如果没有提取到足够的建议，添加一些默认建议
        if (recommendations.length < 3) {
            recommendations.push('保持理性投资心态，避免冲动决策');
            recommendations.push('建立多元化收入来源，不要把鸡蛋放在一个篮子里');
            recommendations.push('定期评估投资组合，及时调整策略');
            recommendations.push('关注新兴行业机会，把握时代发展趋势');
        }
        
        return recommendations.slice(0, 6); // 最多返回6条建议
    }
    
    // 从AI响应中提取幸运元素
    extractLuckyElements(response, element) {
        const luckyElements = [];
        const elementKey = this.getElementKey(element);
        
        // 从开运改善方法部分提取
        const enhancementMatch = response.match(/开运改善方法[：:]\s*([\s\S]*?)(?=\d+\.|$)/i);
        if (enhancementMatch && enhancementMatch[1]) {
            const text = enhancementMatch[1].trim();
            
            // 提取颜色
            const colorMatch = text.match(/颜色[：:：]?\s*([^，。；\n]+)/);
            if (colorMatch && colorMatch[1]) {
                luckyElements.push(colorMatch[1].trim());
            } else {
                // 使用默认颜色
                const colors = this.wealthKnowledge.elements[elementKey].colors;
                if (colors && colors.length > 0) {
                    luckyElements.push(colors[0]);
                }
            }
            
            // 提取数字
            const numberMatch = text.match(/数字[：:：]?\s*([^，。；\n]+)/);
            if (numberMatch && numberMatch[1]) {
                luckyElements.push(numberMatch[1].trim());
            } else {
                // 使用默认数字
                const numbers = this.wealthKnowledge.elements[elementKey].numbers;
                if (numbers && numbers.length > 0) {
                    luckyElements.push(numbers[0] + '、' + numbers[1]);
                }
            }
            
            // 提取方位
            const directionMatch = text.match(/方位[：:：]?\s*([^，。；\n]+)/);
            if (directionMatch && directionMatch[1]) {
                luckyElements.push(directionMatch[1].trim());
            } else {
                // 使用默认方位
                const directions = this.wealthKnowledge.elements[elementKey].directions;
                if (directions && directions.length > 0) {
                    luckyElements.push(directions[0]);
                }
            }
            
            // 提取物品
            const itemMatch = text.match(/物品[：:：]?\s*([^，。；\n]+)/);
            if (itemMatch && itemMatch[1]) {
                luckyElements.push(itemMatch[1].trim());
            } else {
                // 使用默认物品
                const items = this.wealthKnowledge.luckyItems[elementKey];
                if (items && items.length > 0) {
                    luckyElements.push(items[0]);
                }
            }
        }
        
        // 如果提取的元素不足，添加默认元素
        if (luckyElements.length < 4) {
            // 添加默认的幸运元素
            const defaultElements = [
                this.wealthKnowledge.elements[elementKey].colors[0],
                this.wealthKnowledge.elements[elementKey].numbers.join('、'),
                this.wealthKnowledge.elements[elementKey].directions[0],
                this.wealthKnowledge.luckyItems[elementKey][0]
            ];
            
            defaultElements.forEach(item => {
                if (!luckyElements.includes(item)) {
                    luckyElements.push(item);
                }
            });
        }
        
        return luckyElements.slice(0, 6); // 最多返回6个幸运元素
    }

    // 本地分析
    generateLocalAnalysis(userData) {
        const hourInfo = this.wealthKnowledge.hourWealth[userData.birthHour];
        const score = this.calculateWealthScore(userData);
        const elementKey = this.getElementKey(hourInfo.element);
        const yearTrend = this.wealthKnowledge.yearTrends[elementKey] || this.wealthKnowledge.yearTrends.earth;
        
        return {
            success: true,
            wealthAnalysis: {
                method: 'local',
                score: score,
                summary: `您出生于${hourInfo.name}，五行属${hourInfo.element}，财运等级${hourInfo.wealthLevel}/10。${hourInfo.traits.join('，')}。2025年${hourInfo.element}行业趋势${yearTrend.trend}。`,
                sections: {
                    overall: `根据八字时辰分析，您的整体财运为${score}分，属于${this.getScoreLevel(score)}。${hourInfo.traits[0]}，这表明您在财富积累方面有独特优势。`,
                    regular: `您的正财运势${score >= 70 ? '不错' : '平稳'}，工作事业收入将是您主要的财富来源。建议专注于${this.getElementInfo(hourInfo.element).careers[0]}等相关行业发展。`,
                    investment: `投资理财方面，您适合${score >= 75 ? '适度进取' : '稳健保守'}的策略。建议重点关注${this.getElementInfo(hourInfo.element).careers.join('、')}等行业投资。`,
                    timing: `2025年是您的财运${yearTrend.trend}期，特别是春季和夏季财运更为旺盛，建议把握投资机会。${yearTrend.description}`,
                    direction: `您的财运方位在${this.getElementInfo(hourInfo.element).directions.join('、')}，可以在办公室或住宅中适当布局这些方位，有助于提升财运。`,
                    enhancement: `建议使用${this.getElementInfo(hourInfo.element).colors.join('、')}等颜色的物品提升财运，数字${this.getElementInfo(hourInfo.element).numbers.join('、')}对您较为吉利。`
                },
                recommendations: [
                    `发挥${hourInfo.traits[0]}的优势`,
                    `关注${this.getElementInfo(hourInfo.element).careers[0]}行业发展`,
                    `使用${this.getElementInfo(hourInfo.element).colors[0]}色调提升财运`,
                    `选择${this.getElementInfo(hourInfo.element).directions[0]}方位开展事业`,
                    '保持稳健的理财策略',
                    '定期储蓄和投资'
                ],
                luckyElements: [
                    ...this.getElementInfo(hourInfo.element).colors,
                    this.getElementInfo(hourInfo.element).numbers.join('、'),
                    this.getElementInfo(hourInfo.element).directions[0],
                    this.wealthKnowledge.luckyItems[elementKey][0]
                ],
                timestamp: new Date().toISOString()
            }
        };
    }

    // 计算财运评分
    calculateWealthScore(userData) {
        const hourInfo = this.wealthKnowledge.hourWealth[userData.birthHour];
        const baseScore = hourInfo.wealthLevel * 10;
        
        // 年龄因素
        const birthYear = new Date(userData.birthDate).getFullYear();
        const age = 2025 - birthYear;
        let ageBonus = 0;
        
        if (age >= 25 && age <= 45) {
            ageBonus = 5; // 黄金年龄段加分
        } else if (age > 45 && age <= 55) {
            ageBonus = 3; // 成熟年龄段小幅加分
        } else if (age < 25) {
            ageBonus = -3; // 年轻年龄段略微减分
        }
        
        // 性别因素（微调）
        const genderFactor = userData.gender === 'male' ? 1 : 0;
        
        // 最终分数，限制在40-95之间
        return Math.min(Math.max(baseScore + ageBonus + genderFactor, 40), 95);
    }

    // 获取五行信息
    getElementInfo(element) {
        const elementKey = this.getElementKey(element);
        return this.wealthKnowledge.elements[elementKey] || this.wealthKnowledge.elements.earth;
    }
    
    // 获取元素键名
    getElementKey(element) {
        const mapping = {
            '木': 'wood',
            '火': 'fire',
            '土': 'earth',
            '金': 'metal',
            '水': 'water'
        };
        return mapping[element] || 'earth';
    }
    
    // 获取分数等级描述
    getScoreLevel(score) {
        if (score >= 90) return '极佳';
        if (score >= 80) return '很好';
        if (score >= 70) return '良好';
        if (score >= 60) return '一般';
        if (score >= 50) return '较弱';
        return '需改善';
    }
}

// 导出
window.WealthAnalysisAI = WealthAnalysisAI; 