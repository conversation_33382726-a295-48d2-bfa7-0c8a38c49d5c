/**
 * 订单API模块
 * 模拟订单相关的API调用
 */

class OrderAPI {
    /**
     * 创建订单
     * @param {Object} orderData - 订单数据
     * @returns {Promise<Object>} 订单对象
     */
    static async createOrder(orderData) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const order = {
                    id: 'ORDER_' + Date.now(),
                    orderNumber: 'ORD' + Math.random().toString(36).substr(2, 9).toUpperCase(),
                    serviceType: orderData.serviceType,
                    serviceName: orderData.serviceName,
                    amount: orderData.amount,
                    userData: orderData.userData,
                    description: orderData.description,
                    status: 'pending',
                    createTime: new Date().toISOString(),
                    updateTime: new Date().toISOString()
                };
                
                console.log('订单创建成功:', order);
                resolve(order);
            }, 500);
        });
    }

    /**
     * 支付订单
     * @param {string} orderId - 订单ID
     * @returns {Promise<Object>} 支付结果
     */
    static async payOrder(orderId) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const result = {
                    orderId: orderId,
                    paymentId: 'PAY_' + Date.now(),
                    amount: 0, // 金额从订单中获取
                    status: 'success',
                    paymentTime: new Date().toISOString(),
                    paymentMethod: 'wechat', // 默认微信支付
                    transactionId: 'TXN' + Math.random().toString(36).substr(2, 12).toUpperCase()
                };
                
                console.log('支付成功:', result);
                resolve(result);
            }, 1000);
        });
    }

    /**
     * 查询订单
     * @param {string} orderId - 订单ID
     * @returns {Promise<Object>} 订单信息
     */
    static async getOrder(orderId) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const order = {
                    id: orderId,
                    orderNumber: 'ORD' + Math.random().toString(36).substr(2, 9).toUpperCase(),
                    serviceType: 'test',
                    serviceName: '测试服务',
                    amount: 19.9,
                    status: 'paid',
                    createTime: new Date().toISOString(),
                    updateTime: new Date().toISOString()
                };
                
                resolve(order);
            }, 300);
        });
    }

    /**
     * 取消订单
     * @param {string} orderId - 订单ID
     * @returns {Promise<Object>} 取消结果
     */
    static async cancelOrder(orderId) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const result = {
                    orderId: orderId,
                    status: 'cancelled',
                    cancelTime: new Date().toISOString()
                };
                
                console.log('订单取消成功:', result);
                resolve(result);
            }, 300);
        });
    }

    /**
     * 获取订单列表
     * @param {Object} params - 查询参数
     * @returns {Promise<Array>} 订单列表
     */
    static async getOrderList(params = {}) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const orders = [
                    {
                        id: 'ORDER_001',
                        orderNumber: 'ORD123456789',
                        serviceType: 'bazi',
                        serviceName: '八字精批',
                        amount: 19.9,
                        status: 'paid',
                        createTime: '2024-01-15T10:30:00Z'
                    },
                    {
                        id: 'ORDER_002',
                        orderNumber: 'ORD987654321',
                        serviceType: 'portrait',
                        serviceName: '姻缘画像',
                        amount: 19.9,
                        status: 'pending',
                        createTime: '2024-01-14T15:20:00Z'
                    }
                ];
                
                resolve(orders);
            }, 500);
        });
    }
}

// 将OrderAPI暴露到全局
window.OrderAPI = OrderAPI; 